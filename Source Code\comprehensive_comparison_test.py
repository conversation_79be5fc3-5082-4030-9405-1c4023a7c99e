#!/usr/bin/env python3
"""
Comprehensive Comparison Test Suite
Compares PyTorch implementation with original NumPy implementation
"""

import torch
import numpy as np
import time
import warnings
from typing import Dict, List, Tuple, Any
import sys
import os

# Import original NumPy implementation
try:
    from dnn import (
        f0 as numpy_f0, f1 as numpy_f1, f2 as numpy_f2, f3 as numpy_f3, g1 as numpy_g1,
        loss as numpy_loss, distill as numpy_distill,
        reparameterize as numpy_reparameterize,
        gradient_descent as numpy_gradient_descent,
        init_L2_descent as numpy_init_L2_descent,
        init_swarm_descent as numpy_init_swarm_descent
    )
    NUMPY_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import original NumPy implementation: {e}")
    NUMPY_AVAILABLE = False

# Import PyTorch implementation
from pytorch_dnn import (
    GaussianNet, RationalNet, DirichletNet, ReparameterizedRationalNet,
    DNNConfig, DNNDataset, DNNTrainer, DNNEvaluator,
    L1AbsLoss, L1AvgLoss, L2Loss,
    generate_data, create_model, create_loss_function,
    reparameterize_params, check_reparameterization
)

class ComprehensiveComparisonTest:
    """Comprehensive test suite for comparing PyTorch and NumPy implementations"""
    
    def __init__(self, tolerance=1e-5, verbose=True):
        """
        Initialize comparison test suite
        tolerance: numerical tolerance for comparisons
        verbose: whether to print detailed output
        """
        self.tolerance = tolerance
        self.verbose = verbose
        self.test_results = {}
        self.performance_results = {}
        
        if not NUMPY_AVAILABLE:
            raise ImportError("Original NumPy implementation not available for comparison")
    
    def log(self, message: str, level: str = "INFO"):
        """Log message if verbose mode is enabled"""
        if self.verbose:
            print(f"[{level}] {message}")
    
    def assert_arrays_close(self, arr1, arr2, name: str, tolerance=None):
        """Assert that two arrays are numerically close"""
        if tolerance is None:
            tolerance = self.tolerance
        
        # Convert to numpy if needed
        if isinstance(arr1, torch.Tensor):
            arr1 = arr1.detach().cpu().numpy()
        if isinstance(arr2, torch.Tensor):
            arr2 = arr2.detach().cpu().numpy()
        
        # Handle different shapes
        if arr1.shape != arr2.shape:
            raise AssertionError(f"{name}: Shape mismatch - {arr1.shape} vs {arr2.shape}")
        
        # Calculate differences
        diff = np.abs(arr1 - arr2)
        max_diff = np.max(diff)
        mean_diff = np.mean(diff)
        
        if max_diff > tolerance:
            raise AssertionError(
                f"{name}: Arrays not close enough. Max diff: {max_diff:.2e}, "
                f"Mean diff: {mean_diff:.2e}, Tolerance: {tolerance:.2e}"
            )
        
        self.log(f"✓ {name}: Max diff={max_diff:.2e}, Mean diff={mean_diff:.2e}")
        return max_diff, mean_diff

class FunctionEquivalenceTest(ComprehensiveComparisonTest):
    """Test equivalence of function implementations"""
    
    def test_function_f0(self):
        """Test f0 (Gaussian) function equivalence"""
        self.log("Testing f0 (Gaussian) function equivalence...")
        
        # Set up test parameters
        np.random.seed(565)
        torch.manual_seed(565)
        
        nfeatures = 7
        nobs = 100
        layers = 20
        
        # Generate test data
        params = np.random.uniform(0, 1, (layers, nfeatures))
        x = np.random.uniform(0, 1, (nfeatures, nobs))
        args = {'model': 'gaussian', 'equalize': True}
        
        # NumPy implementation
        y_numpy = numpy_f0(params, x, args)
        
        # PyTorch implementation
        pytorch_net = GaussianNet(layers, nfeatures, args)
        pytorch_net.set_params_from_numpy(params)
        
        with torch.no_grad():
            x_tensor = torch.tensor(x, dtype=torch.float32)
            y_pytorch = pytorch_net(x_tensor).numpy()
        
        # Compare results
        max_diff, mean_diff = self.assert_arrays_close(y_numpy, y_pytorch, "f0 function")
        
        return {
            'function': 'f0',
            'max_diff': max_diff,
            'mean_diff': mean_diff,
            'passed': True
        }
    
    def test_function_f1(self):
        """Test f1 (Rational) function equivalence"""
        self.log("Testing f1 (Rational) function equivalence...")
        
        # Set up test parameters
        np.random.seed(565)
        torch.manual_seed(565)
        
        nfeatures = 12
        nobs = 300
        layers = 4
        
        # Generate test data
        params = np.zeros((layers, nfeatures))
        params[0, :] = np.random.uniform(0.25, 0.50, nfeatures)
        params[1, :] = np.random.uniform(0.25, 1.00, nfeatures)
        params[2, :] = np.random.uniform(0.25, 1.00, nfeatures)
        params[3, :] = np.random.uniform(0.00, 1.00, nfeatures)
        params[0, :4] = [0, 0, 0, 0]
        
        x = np.linspace(0, 1, num=nobs)
        args = {
            'small': 0.001,
            'function_type': 'rational',
            'model': 'latent',
            'equalize': False,
            'centers': params[3, :]
        }
        
        # NumPy implementation
        y_numpy = numpy_f1(params, x, args)
        
        # PyTorch implementation
        pytorch_net = RationalNet(layers, nfeatures, args)
        pytorch_net.set_params_from_numpy(params)
        
        with torch.no_grad():
            x_tensor = torch.tensor(x, dtype=torch.float32)
            y_pytorch = pytorch_net(x_tensor).numpy()
        
        # Compare results
        max_diff, mean_diff = self.assert_arrays_close(y_numpy, y_pytorch, "f1 function")
        
        return {
            'function': 'f1',
            'max_diff': max_diff,
            'mean_diff': mean_diff,
            'passed': True
        }
    
    def test_function_f2(self):
        """Test f2 (Dirichlet real) function equivalence"""
        self.log("Testing f2 (Dirichlet real) function equivalence...")
        
        # Set up test parameters
        np.random.seed(565)
        torch.manual_seed(565)
        
        nfeatures = 30
        nobs = 300
        layers = 3
        
        # Generate test data
        params = np.zeros((layers, nfeatures))
        params[0, 0] = 0.75
        params[1, 0] = 0.10
        for k in range(nfeatures):
            params[2, k] = 1.0
        
        x = np.random.uniform(0, 25, nobs)
        args = {'equalize': True}
        
        # NumPy implementation
        y_numpy = numpy_f2(params, x, args)
        
        # PyTorch implementation
        pytorch_net = DirichletNet(layers, nfeatures, args, part='real')
        pytorch_net.set_params_from_numpy(params)
        
        with torch.no_grad():
            x_tensor = torch.tensor(x, dtype=torch.float32)
            y_pytorch = pytorch_net(x_tensor).numpy()
        
        # Compare results
        max_diff, mean_diff = self.assert_arrays_close(y_numpy, y_pytorch, "f2 function")
        
        return {
            'function': 'f2',
            'max_diff': max_diff,
            'mean_diff': mean_diff,
            'passed': True
        }
    
    def test_function_f3(self):
        """Test f3 (Dirichlet imaginary) function equivalence"""
        self.log("Testing f3 (Dirichlet imaginary) function equivalence...")
        
        # Set up test parameters (same as f2)
        np.random.seed(565)
        torch.manual_seed(565)
        
        nfeatures = 30
        nobs = 300
        layers = 3
        
        # Generate test data
        params = np.zeros((layers, nfeatures))
        params[0, 0] = 0.75
        params[1, 0] = 0.10
        for k in range(nfeatures):
            params[2, k] = 1.0
        
        x = np.random.uniform(0, 25, nobs)
        args = {'equalize': True}
        
        # NumPy implementation
        y_numpy = numpy_f3(params, x, args)
        
        # PyTorch implementation
        pytorch_net = DirichletNet(layers, nfeatures, args, part='imaginary')
        pytorch_net.set_params_from_numpy(params)
        
        with torch.no_grad():
            x_tensor = torch.tensor(x, dtype=torch.float32)
            y_pytorch = pytorch_net(x_tensor).numpy()
        
        # Compare results
        max_diff, mean_diff = self.assert_arrays_close(y_numpy, y_pytorch, "f3 function")
        
        return {
            'function': 'f3',
            'max_diff': max_diff,
            'mean_diff': mean_diff,
            'passed': True
        }
    
    def test_function_g1(self):
        """Test g1 (Reparameterized rational) function equivalence"""
        self.log("Testing g1 (Reparameterized rational) function equivalence...")
        
        # Set up test parameters
        np.random.seed(565)
        torch.manual_seed(565)
        
        nfeatures = 12
        nobs = 300
        layers = 4
        
        # Generate test data (reparameterized parameters)
        rparams = np.zeros((layers, nfeatures))
        rparams[0, :] = np.random.uniform(0.1, 0.5, nfeatures)
        rparams[1, :] = np.random.uniform(0.1, 1.0, nfeatures)
        rparams[2, :] = np.random.uniform(0.0, 1.0, nfeatures)
        
        x = np.linspace(0, 1, num=nobs)
        args = {
            'small': 0.001,
            'function_type': 'rational',
            'model': 'latent',
            'equalize': False,
            'centers': rparams[2, :]
        }
        
        # NumPy implementation
        y_numpy = numpy_g1(rparams, x, args)
        
        # PyTorch implementation
        pytorch_net = ReparameterizedRationalNet(layers, nfeatures, args)
        pytorch_net.set_params_from_numpy(rparams)
        
        with torch.no_grad():
            x_tensor = torch.tensor(x, dtype=torch.float32)
            y_pytorch = pytorch_net(x_tensor).numpy()
        
        # Compare results
        max_diff, mean_diff = self.assert_arrays_close(y_numpy, y_pytorch, "g1 function")
        
        return {
            'function': 'g1',
            'max_diff': max_diff,
            'mean_diff': mean_diff,
            'passed': True
        }
    
    def run_all_function_tests(self):
        """Run all function equivalence tests"""
        self.log("=" * 60)
        self.log("FUNCTION EQUIVALENCE TESTS")
        self.log("=" * 60)
        
        function_tests = [
            self.test_function_f0,
            self.test_function_f1,
            self.test_function_f2,
            self.test_function_f3,
            self.test_function_g1
        ]
        
        results = []
        for test_func in function_tests:
            try:
                result = test_func()
                results.append(result)
                self.log(f"✓ {result['function']} test passed")
            except Exception as e:
                self.log(f"✗ {test_func.__name__} failed: {str(e)}", "ERROR")
                results.append({
                    'function': test_func.__name__,
                    'max_diff': float('inf'),
                    'mean_diff': float('inf'),
                    'passed': False,
                    'error': str(e)
                })
        
        self.test_results['function_equivalence'] = results
        return results


class LossEquivalenceTest(ComprehensiveComparisonTest):
    """Test equivalence of loss function implementations"""

    def test_loss_functions(self):
        """Test all loss function equivalence"""
        self.log("Testing loss function equivalence...")

        # Set up test data
        np.random.seed(565)
        torch.manual_seed(565)

        y_true = np.random.randn(100)
        y_pred = y_true + np.random.normal(0, 0.1, 100)  # Add some noise

        results = []

        # Test L1_abs loss
        numpy_loss_abs = numpy_loss(lambda p, x, a: y_pred, None, y_true, None, 'L1_abs', {})
        pytorch_loss_abs = L1AbsLoss()(torch.tensor(y_pred), torch.tensor(y_true)).item()

        max_diff, mean_diff = self.assert_arrays_close(
            np.array([numpy_loss_abs]), np.array([pytorch_loss_abs]), "L1_abs loss"
        )
        results.append({'loss_type': 'L1_abs', 'max_diff': max_diff, 'passed': True})

        # Test L1_avg loss
        numpy_loss_avg = numpy_loss(lambda p, x, a: y_pred, None, y_true, None, 'L1_avg', {})
        pytorch_loss_avg = L1AvgLoss()(torch.tensor(y_pred), torch.tensor(y_true)).item()

        max_diff, mean_diff = self.assert_arrays_close(
            np.array([numpy_loss_avg]), np.array([pytorch_loss_avg]), "L1_avg loss"
        )
        results.append({'loss_type': 'L1_avg', 'max_diff': max_diff, 'passed': True})

        # Test L2 loss
        numpy_loss_l2 = numpy_loss(lambda p, x, a: y_pred, None, y_true, None, 'L2', {})
        pytorch_loss_l2 = L2Loss()(torch.tensor(y_pred), torch.tensor(y_true)).item()

        max_diff, mean_diff = self.assert_arrays_close(
            np.array([numpy_loss_l2]), np.array([pytorch_loss_l2]), "L2 loss"
        )
        results.append({'loss_type': 'L2', 'max_diff': max_diff, 'passed': True})

        return results


class DataProcessingTest(ComprehensiveComparisonTest):
    """Test data processing equivalence"""

    def test_distillation(self):
        """Test distillation function equivalence"""
        self.log("Testing distillation equivalence...")

        # Set up test data
        np.random.seed(565)
        torch.manual_seed(565)

        n = 1000
        x_1d = np.random.randn(n)
        x_2d = np.random.randn(5, n)
        y = np.random.randn(n)
        rate = 0.7

        # Test 1D distillation
        np.random.seed(565)
        x_dist_numpy, y_dist_numpy = numpy_distill(rate, x_1d, y)

        np.random.seed(565)
        torch.manual_seed(565)
        dataset_1d = DNNDataset(x_1d, y, distill_rate=rate, seed=565)

        # Compare sizes (should be same due to same seed)
        assert len(y_dist_numpy) == len(dataset_1d.y), "1D distillation size mismatch"

        # Test 2D distillation
        np.random.seed(565)
        x_dist_numpy_2d, y_dist_numpy_2d = numpy_distill(rate, x_2d, y)

        np.random.seed(565)
        torch.manual_seed(565)
        dataset_2d = DNNDataset(x_2d, y, distill_rate=rate, seed=565)

        # Compare sizes
        assert len(y_dist_numpy_2d) == len(dataset_2d.y), "2D distillation size mismatch"

        self.log("✓ Distillation equivalence test passed")
        return {'test': 'distillation', 'passed': True}

    def test_reparameterization(self):
        """Test reparameterization equivalence"""
        self.log("Testing reparameterization equivalence...")

        # Set up test parameters
        np.random.seed(565)
        layers, nfeatures = 4, 10
        params = np.random.uniform(0.1, 1.0, (layers, nfeatures))

        # NumPy implementation
        rparams_numpy = numpy_reparameterize(params)

        # PyTorch implementation
        rparams_pytorch = reparameterize_params(params)

        # Compare results
        max_diff, mean_diff = self.assert_arrays_close(
            rparams_numpy, rparams_pytorch, "reparameterization"
        )

        return {
            'test': 'reparameterization',
            'max_diff': max_diff,
            'mean_diff': mean_diff,
            'passed': True
        }


class OptimizationEquivalenceTest(ComprehensiveComparisonTest):
    """Test optimization equivalence (challenging due to randomness)"""

    def test_optimization_convergence(self, function_type='f2', epochs=50):
        """Test that both implementations converge to similar solutions"""
        self.log(f"Testing optimization convergence for {function_type}...")

        # Set identical seeds
        np.random.seed(565)
        torch.manual_seed(565)

        # Generate identical test data
        if function_type == 'f2':
            # Use f2 as it's most stable
            nfeatures = 10  # Smaller for faster testing
            n = 100
            layers = 3

            # Generate ground truth parameters
            params_true = np.zeros((layers, nfeatures))
            params_true[0, 0] = 0.75
            params_true[1, 0] = 0.10
            for k in range(nfeatures):
                params_true[2, k] = 1.0

            x = np.random.uniform(0, 10, n)  # Smaller range for stability
            args = {'equalize': True}

            # Generate clean data
            y_base = numpy_f2(params_true, x, args)
            y = y_base + np.random.normal(0, 0.01 * np.std(y_base), n)  # Small noise

            # Test NumPy implementation
            np.random.seed(565)
            args_numpy = args.copy()
            args_numpy['eps'] = 1e-6

            try:
                params_estimated_numpy, history_numpy = numpy_gradient_descent(
                    epochs, 0.1, layers, nfeatures, numpy_f2, y, x, 0.0, 'L2', 'L2', args_numpy
                )
                numpy_final_loss = history_numpy[-1]
                numpy_success = True
            except Exception as e:
                self.log(f"NumPy optimization failed: {e}", "WARNING")
                numpy_success = False
                numpy_final_loss = float('inf')

            # Test PyTorch implementation
            torch.manual_seed(565)
            np.random.seed(565)

            model = DirichletNet(layers, nfeatures, args, part='real')
            loss_fn = L2Loss()
            trainer = DNNTrainer(model, loss_fn, optimizer_type='L2', lr=0.1, temperature=0.0)

            x_tensor = torch.tensor(x, dtype=torch.float32)
            y_tensor = torch.tensor(y, dtype=torch.float32)

            history_pytorch = trainer.train(x_tensor, y_tensor, epochs=epochs, print_every=1000)
            pytorch_final_loss = history_pytorch[-1]

            # Compare convergence
            convergence_ratio = abs(pytorch_final_loss - numpy_final_loss) / max(pytorch_final_loss, numpy_final_loss)

            self.log(f"NumPy final loss: {numpy_final_loss:.6f}")
            self.log(f"PyTorch final loss: {pytorch_final_loss:.6f}")
            self.log(f"Convergence ratio: {convergence_ratio:.6f}")

            # Allow for some difference due to optimization randomness
            convergence_tolerance = 0.5  # 50% difference allowed
            convergence_passed = convergence_ratio < convergence_tolerance or not numpy_success

            return {
                'function_type': function_type,
                'numpy_final_loss': numpy_final_loss,
                'pytorch_final_loss': pytorch_final_loss,
                'convergence_ratio': convergence_ratio,
                'convergence_passed': convergence_passed,
                'numpy_success': numpy_success
            }

        else:
            # For other function types, implement similar logic
            self.log(f"Optimization test for {function_type} not implemented yet")
            return {'function_type': function_type, 'passed': False, 'reason': 'Not implemented'}


class PerformanceBenchmark(ComprehensiveComparisonTest):
    """Performance benchmarking between implementations"""

    def benchmark_function_speed(self, function_type='f2', n_iterations=100):
        """Benchmark function evaluation speed"""
        self.log(f"Benchmarking {function_type} function speed...")

        # Set up test data
        if function_type == 'f2':
            nfeatures = 30
            nobs = 1000
            layers = 3

            params = np.zeros((layers, nfeatures))
            params[0, 0] = 0.75
            params[1, 0] = 0.10
            for k in range(nfeatures):
                params[2, k] = 1.0

            x = np.random.uniform(0, 25, nobs)
            args = {'equalize': True}

            # Benchmark NumPy
            start_time = time.time()
            for _ in range(n_iterations):
                y_numpy = numpy_f2(params, x, args)
            numpy_time = time.time() - start_time

            # Benchmark PyTorch
            pytorch_net = DirichletNet(layers, nfeatures, args, part='real')
            pytorch_net.set_params_from_numpy(params)
            x_tensor = torch.tensor(x, dtype=torch.float32)

            start_time = time.time()
            for _ in range(n_iterations):
                with torch.no_grad():
                    y_pytorch = pytorch_net(x_tensor)
            pytorch_time = time.time() - start_time

            speedup = numpy_time / pytorch_time

            self.log(f"NumPy time: {numpy_time:.4f}s")
            self.log(f"PyTorch time: {pytorch_time:.4f}s")
            self.log(f"Speedup: {speedup:.2f}x")

            return {
                'function_type': function_type,
                'numpy_time': numpy_time,
                'pytorch_time': pytorch_time,
                'speedup': speedup,
                'iterations': n_iterations
            }

        return {'function_type': function_type, 'error': 'Not implemented'}


def run_comprehensive_comparison():
    """Run all comprehensive comparison tests"""
    print("=" * 80)
    print("COMPREHENSIVE PYTORCH vs NUMPY COMPARISON TEST SUITE")
    print("=" * 80)

    if not NUMPY_AVAILABLE:
        print("❌ Cannot run comparison tests - original NumPy implementation not available")
        return None

    # Initialize test suite
    test_suite = ComprehensiveComparisonTest(tolerance=1e-5, verbose=True)

    all_results = {}

    try:
        # Test function equivalence
        func_test = FunctionEquivalenceTest(tolerance=1e-5, verbose=True)
        func_results = func_test.run_all_function_tests()
        all_results['function_equivalence'] = func_results

        # Test loss equivalence
        loss_test = LossEquivalenceTest(tolerance=1e-5, verbose=True)
        loss_results = loss_test.test_loss_functions()
        all_results['loss_equivalence'] = loss_results

        # Test data processing
        data_test = DataProcessingTest(tolerance=1e-5, verbose=True)
        distill_result = data_test.test_distillation()
        reparam_result = data_test.test_reparameterization()
        all_results['data_processing'] = [distill_result, reparam_result]

        # Test optimization convergence
        opt_test = OptimizationEquivalenceTest(tolerance=1e-5, verbose=True)
        opt_result = opt_test.test_optimization_convergence('f2', epochs=30)
        all_results['optimization'] = [opt_result]

        # Performance benchmark
        perf_test = PerformanceBenchmark(tolerance=1e-5, verbose=True)
        perf_result = perf_test.benchmark_function_speed('f2', n_iterations=50)
        all_results['performance'] = [perf_result]

        return all_results

    except Exception as e:
        print(f"❌ Comprehensive test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    results = run_comprehensive_comparison()

    if results:
        print("\n" + "=" * 80)
        print("COMPREHENSIVE TEST RESULTS SUMMARY")
        print("=" * 80)

        # Function equivalence summary
        func_results = results.get('function_equivalence', [])
        passed_funcs = sum(1 for r in func_results if r.get('passed', False))
        print(f"Function Equivalence: {passed_funcs}/{len(func_results)} passed")

        # Loss equivalence summary
        loss_results = results.get('loss_equivalence', [])
        passed_losses = sum(1 for r in loss_results if r.get('passed', False))
        print(f"Loss Equivalence: {passed_losses}/{len(loss_results)} passed")

        # Data processing summary
        data_results = results.get('data_processing', [])
        passed_data = sum(1 for r in data_results if r.get('passed', False))
        print(f"Data Processing: {passed_data}/{len(data_results)} passed")

        # Optimization summary
        opt_results = results.get('optimization', [])
        passed_opt = sum(1 for r in opt_results if r.get('convergence_passed', False))
        print(f"Optimization Convergence: {passed_opt}/{len(opt_results)} passed")

        # Performance summary
        perf_results = results.get('performance', [])
        for perf in perf_results:
            if 'speedup' in perf:
                print(f"Performance: PyTorch is {perf['speedup']:.2f}x vs NumPy")

        print("\n🎉 Comprehensive comparison completed!")
    else:
        print("❌ Comprehensive comparison failed!")
