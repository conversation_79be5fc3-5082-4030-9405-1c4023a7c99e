#!/usr/bin/env python3
"""
Quick Comparison Test - Core Function Equivalence
Tests the most critical aspects: function outputs and basic operations
"""

import torch
import numpy as np
import sys
import warnings

# Import original NumPy implementation
try:
    from dnn import (
        f0 as numpy_f0, f1 as numpy_f1, f2 as numpy_f2, f3 as numpy_f3, g1 as numpy_g1,
        loss as numpy_loss, distill as numpy_distill,
        reparameterize as numpy_reparameterize
    )
    NUMPY_AVAILABLE = True
    print("✓ Original NumPy implementation imported successfully")
except ImportError as e:
    print(f"❌ Could not import original NumPy implementation: {e}")
    NUMPY_AVAILABLE = False

# Import PyTorch implementation
from pytorch_dnn import (
    GaussianNet, RationalNet, DirichletNet, ReparameterizedRationalNet,
    L1AbsLoss, L1AvgLoss, L2Loss,
    reparameterize_params
)

def test_function_equivalence():
    """Test that all functions produce equivalent outputs"""
    print("\n" + "="*60)
    print("TESTING FUNCTION EQUIVALENCE")
    print("="*60)
    
    tolerance = 1e-5
    results = {}
    
    # Test f0 (Gaussian)
    print("\nTesting f0 (Gaussian function)...")
    np.random.seed(565)
    torch.manual_seed(565)
    
    nfeatures, nobs, layers = 5, 50, 12
    params = np.random.uniform(0, 1, (layers, nfeatures))
    x = np.random.uniform(0, 1, (nfeatures, nobs))
    args = {'model': 'gaussian', 'equalize': True}
    
    # NumPy version
    y_numpy = numpy_f0(params, x, args)
    
    # PyTorch version
    pytorch_net = GaussianNet(layers, nfeatures, args)
    pytorch_net.set_params_from_numpy(params)
    with torch.no_grad():
        x_tensor = torch.tensor(x, dtype=torch.float32)
        y_pytorch = pytorch_net(x_tensor).numpy()
    
    diff = np.max(np.abs(y_numpy - y_pytorch))
    passed = diff < tolerance
    print(f"f0 max difference: {diff:.2e} (tolerance: {tolerance:.2e}) - {'✓ PASS' if passed else '✗ FAIL'}")
    results['f0'] = {'diff': diff, 'passed': passed}
    
    # Test f1 (Rational)
    print("\nTesting f1 (Rational function)...")
    np.random.seed(565)
    torch.manual_seed(565)
    
    nfeatures, nobs, layers = 8, 100, 4
    params = np.zeros((layers, nfeatures))
    params[0, :] = np.random.uniform(0.25, 0.50, nfeatures)
    params[1, :] = np.random.uniform(0.25, 1.00, nfeatures)
    params[2, :] = np.random.uniform(0.25, 1.00, nfeatures)
    params[3, :] = np.random.uniform(0.00, 1.00, nfeatures)
    
    x = np.linspace(0, 1, num=nobs)
    args = {
        'small': 0.001,
        'function_type': 'rational',
        'model': 'latent',
        'equalize': False,
        'centers': params[3, :]
    }
    
    # NumPy version
    y_numpy = numpy_f1(params, x, args)
    
    # PyTorch version
    pytorch_net = RationalNet(layers, nfeatures, args)
    pytorch_net.set_params_from_numpy(params)
    with torch.no_grad():
        x_tensor = torch.tensor(x, dtype=torch.float32)
        y_pytorch = pytorch_net(x_tensor).numpy()
    
    diff = np.max(np.abs(y_numpy - y_pytorch))
    passed = diff < tolerance
    print(f"f1 max difference: {diff:.2e} (tolerance: {tolerance:.2e}) - {'✓ PASS' if passed else '✗ FAIL'}")
    results['f1'] = {'diff': diff, 'passed': passed}
    
    # Test f2 (Dirichlet real)
    print("\nTesting f2 (Dirichlet real part)...")
    np.random.seed(565)
    torch.manual_seed(565)
    
    nfeatures, nobs, layers = 15, 100, 3
    params = np.zeros((layers, nfeatures))
    params[0, 0] = 0.75
    params[1, 0] = 0.10
    for k in range(nfeatures):
        params[2, k] = 1.0
    
    x = np.random.uniform(0, 10, nobs)  # Smaller range for numerical stability
    args = {'equalize': True}
    
    # NumPy version
    y_numpy = numpy_f2(params, x, args)
    
    # PyTorch version
    pytorch_net = DirichletNet(layers, nfeatures, args, part='real')
    pytorch_net.set_params_from_numpy(params)
    with torch.no_grad():
        x_tensor = torch.tensor(x, dtype=torch.float32)
        y_pytorch = pytorch_net(x_tensor).numpy()
    
    diff = np.max(np.abs(y_numpy - y_pytorch))
    passed = diff < tolerance
    print(f"f2 max difference: {diff:.2e} (tolerance: {tolerance:.2e}) - {'✓ PASS' if passed else '✗ FAIL'}")
    results['f2'] = {'diff': diff, 'passed': passed}
    
    # Test f3 (Dirichlet imaginary)
    print("\nTesting f3 (Dirichlet imaginary part)...")
    # Use same parameters as f2
    
    # NumPy version
    y_numpy = numpy_f3(params, x, args)
    
    # PyTorch version
    pytorch_net = DirichletNet(layers, nfeatures, args, part='imaginary')
    pytorch_net.set_params_from_numpy(params)
    with torch.no_grad():
        x_tensor = torch.tensor(x, dtype=torch.float32)
        y_pytorch = pytorch_net(x_tensor).numpy()
    
    diff = np.max(np.abs(y_numpy - y_pytorch))
    passed = diff < tolerance
    print(f"f3 max difference: {diff:.2e} (tolerance: {tolerance:.2e}) - {'✓ PASS' if passed else '✗ FAIL'}")
    results['f3'] = {'diff': diff, 'passed': passed}
    
    # Test g1 (Reparameterized rational)
    print("\nTesting g1 (Reparameterized rational)...")
    np.random.seed(565)
    torch.manual_seed(565)
    
    nfeatures, nobs, layers = 8, 100, 4
    rparams = np.zeros((layers, nfeatures))
    rparams[0, :] = np.random.uniform(0.1, 0.5, nfeatures)
    rparams[1, :] = np.random.uniform(0.1, 1.0, nfeatures)
    rparams[2, :] = np.random.uniform(0.0, 1.0, nfeatures)
    
    x = np.linspace(0, 1, num=nobs)
    args = {
        'small': 0.001,
        'function_type': 'rational',
        'model': 'latent',
        'equalize': False,
        'centers': rparams[2, :]
    }
    
    # NumPy version
    y_numpy = numpy_g1(rparams, x, args)
    
    # PyTorch version
    pytorch_net = ReparameterizedRationalNet(layers, nfeatures, args)
    pytorch_net.set_params_from_numpy(rparams)
    with torch.no_grad():
        x_tensor = torch.tensor(x, dtype=torch.float32)
        y_pytorch = pytorch_net(x_tensor).numpy()
    
    diff = np.max(np.abs(y_numpy - y_pytorch))
    passed = diff < tolerance
    print(f"g1 max difference: {diff:.2e} (tolerance: {tolerance:.2e}) - {'✓ PASS' if passed else '✗ FAIL'}")
    results['g1'] = {'diff': diff, 'passed': passed}
    
    return results

def test_loss_equivalence():
    """Test loss function equivalence"""
    print("\n" + "="*60)
    print("TESTING LOSS FUNCTION EQUIVALENCE")
    print("="*60)
    
    tolerance = 1e-6
    results = {}
    
    # Generate test data
    np.random.seed(565)
    y_true = np.random.randn(100)
    y_pred = y_true + np.random.normal(0, 0.1, 100)
    
    # Test L1_abs loss
    print("\nTesting L1_abs loss...")
    numpy_loss_abs = numpy_loss(lambda p, x, a: y_pred, None, y_true, None, 'L1_abs', {})
    pytorch_loss_abs = L1AbsLoss()(torch.tensor(y_pred), torch.tensor(y_true)).item()
    
    diff = abs(numpy_loss_abs - pytorch_loss_abs)
    passed = diff < tolerance
    print(f"L1_abs difference: {diff:.2e} (tolerance: {tolerance:.2e}) - {'✓ PASS' if passed else '✗ FAIL'}")
    results['L1_abs'] = {'diff': diff, 'passed': passed}
    
    # Test L1_avg loss
    print("\nTesting L1_avg loss...")
    numpy_loss_avg = numpy_loss(lambda p, x, a: y_pred, None, y_true, None, 'L1_avg', {})
    pytorch_loss_avg = L1AvgLoss()(torch.tensor(y_pred), torch.tensor(y_true)).item()
    
    diff = abs(numpy_loss_avg - pytorch_loss_avg)
    passed = diff < tolerance
    print(f"L1_avg difference: {diff:.2e} (tolerance: {tolerance:.2e}) - {'✓ PASS' if passed else '✗ FAIL'}")
    results['L1_avg'] = {'diff': diff, 'passed': passed}
    
    # Test L2 loss
    print("\nTesting L2 loss...")
    numpy_loss_l2 = numpy_loss(lambda p, x, a: y_pred, None, y_true, None, 'L2', {})
    pytorch_loss_l2 = L2Loss()(torch.tensor(y_pred), torch.tensor(y_true)).item()
    
    diff = abs(numpy_loss_l2 - pytorch_loss_l2)
    passed = diff < tolerance
    print(f"L2 difference: {diff:.2e} (tolerance: {tolerance:.2e}) - {'✓ PASS' if passed else '✗ FAIL'}")
    results['L2'] = {'diff': diff, 'passed': passed}
    
    return results

def test_reparameterization():
    """Test reparameterization equivalence"""
    print("\n" + "="*60)
    print("TESTING REPARAMETERIZATION EQUIVALENCE")
    print("="*60)
    
    tolerance = 1e-10
    
    # Generate test parameters
    np.random.seed(565)
    layers, nfeatures = 4, 10
    params = np.random.uniform(0.1, 1.0, (layers, nfeatures))
    
    # NumPy version
    rparams_numpy = numpy_reparameterize(params)
    
    # PyTorch version
    rparams_pytorch = reparameterize_params(params)
    
    diff = np.max(np.abs(rparams_numpy - rparams_pytorch))
    passed = diff < tolerance
    print(f"Reparameterization max difference: {diff:.2e} (tolerance: {tolerance:.2e}) - {'✓ PASS' if passed else '✗ FAIL'}")
    
    return {'diff': diff, 'passed': passed}

def run_quick_comparison():
    """Run quick comparison tests"""
    print("="*80)
    print("QUICK PYTORCH vs NUMPY COMPARISON TEST")
    print("="*80)
    
    if not NUMPY_AVAILABLE:
        print("❌ Cannot run comparison - original NumPy implementation not available")
        return None
    
    all_results = {}
    
    try:
        # Test function equivalence
        func_results = test_function_equivalence()
        all_results['functions'] = func_results
        
        # Test loss equivalence
        loss_results = test_loss_equivalence()
        all_results['losses'] = loss_results
        
        # Test reparameterization
        reparam_result = test_reparameterization()
        all_results['reparameterization'] = reparam_result
        
        # Summary
        print("\n" + "="*80)
        print("QUICK COMPARISON SUMMARY")
        print("="*80)
        
        # Function tests
        func_passed = sum(1 for r in func_results.values() if r['passed'])
        print(f"Function Equivalence: {func_passed}/{len(func_results)} passed")
        for name, result in func_results.items():
            status = "✓" if result['passed'] else "✗"
            print(f"  {status} {name}: {result['diff']:.2e}")
        
        # Loss tests
        loss_passed = sum(1 for r in loss_results.values() if r['passed'])
        print(f"\nLoss Function Equivalence: {loss_passed}/{len(loss_results)} passed")
        for name, result in loss_results.items():
            status = "✓" if result['passed'] else "✗"
            print(f"  {status} {name}: {result['diff']:.2e}")
        
        # Reparameterization
        reparam_status = "✓" if reparam_result['passed'] else "✗"
        print(f"\nReparameterization: {reparam_status} {reparam_result['diff']:.2e}")
        
        # Overall assessment
        total_tests = len(func_results) + len(loss_results) + 1
        total_passed = func_passed + loss_passed + (1 if reparam_result['passed'] else 0)
        
        print(f"\nOVERALL: {total_passed}/{total_tests} tests passed")
        
        if total_passed == total_tests:
            print("🎉 ALL TESTS PASSED! PyTorch implementation is equivalent to NumPy!")
        else:
            print("⚠️  Some tests failed. Check individual results above.")
        
        return all_results
        
    except Exception as e:
        print(f"❌ Quick comparison failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = run_quick_comparison()
