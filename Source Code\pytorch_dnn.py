import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
from torch.utils.data import Dataset, DataLoader
from abc import ABC, abstractmethod
import copy
import warnings

# Set matplotlib parameters
mpl.rcParams['axes.linewidth'] = 0.5
plt.rcParams['xtick.labelsize'] = 8
plt.rcParams['ytick.labelsize'] = 8
plt.rcParams['axes.facecolor'] = 'black'

# Set device
device = torch.device('cpu')  # Using CPU as requested

class BaseDNN(nn.Module, ABC):
    """Abstract base class for all DNN function approximators"""
    
    def __init__(self, layers, nfeatures, args=None):
        super(BaseDNN, self).__init__()
        self.layers = layers
        self.nfeatures = nfeatures
        self.args = args if args is not None else {}
        
        # Initialize parameters as learnable tensors
        self.params = nn.Parameter(torch.zeros(layers, nfeatures, dtype=torch.float32))
        self.init_parameters()
        
    @abstractmethod
    def init_parameters(self):
        """Initialize parameters specific to each function type"""
        pass
        
    @abstractmethod
    def forward(self, x):
        """Forward pass specific to each function type"""
        pass
        
    def get_numpy_params(self):
        """Convert parameters to numpy for compatibility"""
        return self.params.detach().cpu().numpy()
        
    def set_params_from_numpy(self, numpy_params):
        """Set parameters from numpy array"""
        self.params.data = torch.tensor(numpy_params, dtype=torch.float32)

class GaussianNet(BaseDNN):
    """Neural network implementing f0 - Gaussian-based function approximation"""
    
    def init_parameters(self):
        """Initialize parameters for Gaussian network"""
        with torch.no_grad():
            # Initialize with uniform distribution [0, 1]
            self.params.uniform_(0, 1)
    
    def forward(self, x):
        """
        Forward pass for Gaussian network (f0)
        x: input tensor of shape (nfeatures, nobs) for multivariate or (nobs,) for univariate
        """
        if len(x.shape) == 1:
            # Convert 1D to 2D for consistency
            x = x.unsqueeze(0)
            
        nfeatures, nobs = x.shape
        ones = torch.ones(nobs, device=x.device)
        z = torch.zeros(nobs, device=x.device)
        
        J = self.layers // 4
        model = self.args.get('model', 'gaussian')
        
        for j in range(J):
            for k in range(nfeatures):
                theta0 = self.params[4*j, k]
                theta1 = self.params[4*j+1, k] * ones
                theta2 = self.params[4*j+2, k]
                theta3 = torch.tensor(1.0, device=x.device)  # Fixed to 1 as in original
                
                if model == 'approx. gaussian':
                    z += theta0 * (ones - ((x[k, :] - theta1) * theta2)**2)
                elif model == 'gaussian':
                    z += theta0 * torch.exp(-(theta3 * (x[k, :] - theta1) / theta2)**2)
        
        # Apply equalization if specified
        if self.args.get('equalize', False):
            z = z - torch.min(z)
            
        return z

class RationalNet(BaseDNN):
    """Neural network implementing f1 - Rational/Polynomial function approximation"""
    
    def init_parameters(self):
        """Initialize parameters for Rational network"""
        with torch.no_grad():
            if self.layers >= 4:
                # Initialize as in original f1
                self.params[0, :].uniform_(0.25, 0.50)  # weights
                self.params[1, :].uniform_(0.25, 1.00)  # offset
                self.params[2, :].uniform_(0.25, 1.00)  # skewness
                self.params[3, :].uniform_(0.00, 1.00)  # centers
                # First 4 features not used for f, only for DNN
                self.params[0, :4] = 0
            else:
                self.params.uniform_(0, 1)
    
    def forward(self, x):
        """
        Forward pass for Rational network (f1)
        x: input tensor of shape (nobs,) for 1D data
        """
        nobs = len(x)
        ones = torch.ones(nobs, device=x.device)
        z = torch.zeros(nobs, device=x.device)
        
        small = self.args.get('small', 0.001)
        function_type = self.args.get('function_type', 'rational')
        model = self.args.get('model', 'latent')
        centers = self.args.get('centers', None)
        
        small_ones = small * ones
        
        for k in range(self.nfeatures):
            if model == 'static' and centers is not None:
                # Centers are fixed
                xnode_k = centers[k] * ones
            elif model == 'latent':
                # Centers must be estimated
                xnode_k = self.params[3, k] * ones
            else:
                xnode_k = torch.zeros_like(ones)
            
            if function_type == 'rational':
                denominator = self.params[1, k] * small_ones + ((x - xnode_k) / self.params[2, k])**2
                z += small * self.params[0, k] / denominator
            elif function_type == 'polynom':
                diff = x - xnode_k
                z += (self.params[0, k] * diff + 
                      self.params[1, k] * diff**2 + 
                      self.params[2, k] * diff**3)
        
        # Apply equalization if specified
        if self.args.get('equalize', False):
            z = z - torch.min(z)
            
        return z

class DirichletNet(BaseDNN):
    """Neural network implementing f2/f3 - Dirichlet eta function (real and imaginary parts)"""
    
    def __init__(self, layers, nfeatures, args=None, part='real'):
        super().__init__(layers, nfeatures, args)
        self.part = part  # 'real' for f2, 'imaginary' for f3
    
    def init_parameters(self):
        """Initialize parameters for Dirichlet network"""
        with torch.no_grad():
            self.params[0, 0] = 0.75  # sigma
            self.params[1, 0] = 0.10  # phi
            for k in range(self.nfeatures):
                self.params[2, k] = 1.0  # coefficients
    
    def forward(self, x):
        """
        Forward pass for Dirichlet network (f2 or f3)
        x: input tensor of shape (nobs,)
        """
        sigma = self.params[0, 0]
        phi = self.params[1, 0]
        theta = phi / (1 - phi)
        
        z = torch.zeros_like(x)
        
        for k in range(self.nfeatures):
            coeff = self.params[2, k] * (-1)**k / (k + 1)**sigma
            arg = (x + theta) * torch.log(torch.tensor(k + 1, device=x.device))
            
            if self.part == 'real':
                z += coeff * torch.cos(arg)
            elif self.part == 'imaginary':
                z += coeff * torch.sin(arg)
        
        # Apply equalization if specified
        if self.args.get('equalize', False):
            z = z - torch.min(z)
            
        return z

class ReparameterizedRationalNet(BaseDNN):
    """Neural network implementing g1 - Reparameterized version of RationalNet"""
    
    def init_parameters(self):
        """Initialize parameters for reparameterized network"""
        with torch.no_grad():
            # Initialize with smaller range for reparameterized version
            self.params.uniform_(0, 1)
    
    def forward(self, x):
        """
        Forward pass for reparameterized Rational network (g1)
        x: input tensor of shape (nobs,)
        """
        nobs = len(x)
        ones = torch.ones(nobs, device=x.device)
        z = torch.zeros(nobs, device=x.device)
        
        small = self.args.get('small', 0.001)
        model = self.args.get('model', 'latent')
        centers = self.args.get('centers', None)
        
        small_ones = small * ones
        
        for k in range(self.nfeatures):
            if model == 'static' and centers is not None:
                # Centers are fixed
                xnode_k = centers[k] * ones
            elif model == 'latent':
                # Centers must be estimated (using reparameterized form)
                xnode_k = self.params[2, k] * ones
            else:
                xnode_k = torch.zeros_like(ones)
            
            # Reparameterized rational function
            denominator = small_ones + ((x - xnode_k) / self.params[1, k])**2
            z += small * self.params[0, k] / denominator
        
        # Apply equalization if specified
        if self.args.get('equalize', False):
            z = z - torch.min(z)
            
        return z

def reparameterize_params(params):
    """
    Reparameterize parameters from 4-layer to 3-layer format
    params: numpy array of shape (layers, nfeatures)
    returns: reparameterized numpy array
    """
    layers, nfeatures = params.shape
    rparams = np.zeros((layers, nfeatures))
    
    for k in range(nfeatures):
        rparams[0, k] = params[0, k] / params[1, k]
        rparams[1, k] = np.sqrt(params[1, k]) * params[2, k]
        rparams[2, k] = params[3, k]
    
    return rparams

def check_reparameterization(original_net, reparam_net, x, tolerance=1e-6):
    """
    Check if reparameterization is correct by comparing outputs
    """
    original_net.eval()
    reparam_net.eval()

    with torch.no_grad():
        # Get original output
        y_original = original_net(x)

        # Reparameterize parameters and set them
        original_params = original_net.get_numpy_params()
        reparam_params = reparameterize_params(original_params)
        reparam_net.set_params_from_numpy(reparam_params)

        # Get reparameterized output
        y_reparam = reparam_net(x)

        # Calculate difference
        delta = torch.max(torch.abs(y_original - y_reparam)).item()

        if delta > tolerance or np.isnan(delta):
            print(f"Reparameterization error: delta = {delta}")
            return False
        else:
            print(f"Reparameterization correct: delta = {delta}")
            return True


# ===== CUSTOM LOSS FUNCTIONS =====

class CustomLoss(nn.Module):
    """Base class for custom loss functions"""

    def __init__(self, mode='L2'):
        super(CustomLoss, self).__init__()
        self.mode = mode

    def forward(self, y_pred, y_true):
        """
        Calculate loss between predicted and true values
        y_pred: predicted values tensor
        y_true: true values tensor
        """
        diff = torch.abs(y_pred - y_true)

        if self.mode == 'L1_abs':
            return torch.max(diff)
        elif self.mode == 'L1_avg':
            return torch.mean(diff)
        elif self.mode == 'L2':
            return torch.mean(diff**2)
        else:
            raise ValueError(f"Unknown loss mode: {self.mode}")

class L1AbsLoss(CustomLoss):
    """L1 Absolute loss - maximum absolute error"""

    def __init__(self):
        super(L1AbsLoss, self).__init__(mode='L1_abs')

class L1AvgLoss(CustomLoss):
    """L1 Average loss - mean absolute error"""

    def __init__(self):
        super(L1AvgLoss, self).__init__(mode='L1_avg')

class L2Loss(CustomLoss):
    """L2 loss - mean squared error"""

    def __init__(self):
        super(L2Loss, self).__init__(mode='L2')


# ===== DATA HANDLING AND DISTILLATION =====

class DNNDataset(Dataset):
    """Custom dataset for DNN training with distillation support"""

    def __init__(self, x, y, distill_rate=1.0, seed=None):
        """
        Initialize dataset with optional distillation
        x: input data (numpy array or tensor)
        y: target data (numpy array or tensor)
        distill_rate: proportion of data to keep (1.0 = no distillation)
        seed: random seed for reproducibility
        """
        if seed is not None:
            torch.manual_seed(seed)
            np.random.seed(seed)

        # Convert to tensors if needed
        if isinstance(x, np.ndarray):
            x = torch.tensor(x, dtype=torch.float32)
        if isinstance(y, np.ndarray):
            y = torch.tensor(y, dtype=torch.float32)

        # Apply distillation
        if distill_rate < 1.0:
            x, y = self._distill(x, y, distill_rate)
            print(f"Distillation factor: {1-distill_rate}")

        self.x = x
        self.y = y

    def _distill(self, x, y, rate):
        """Apply distillation to reduce dataset size"""
        n = len(y)
        nd = int(rate * n)
        indices = torch.randperm(n)[:nd]

        if len(x.shape) == 2 and x.shape[0] != len(y):
            # Handle case where x is (nfeatures, nobs)
            x_distilled = x[:, indices]
        else:
            x_distilled = x[indices]

        y_distilled = y[indices]
        return x_distilled, y_distilled

    def __len__(self):
        return len(self.y)

    def __getitem__(self, idx):
        if len(self.x.shape) == 2 and self.x.shape[0] != len(self.y):
            # Handle case where x is (nfeatures, nobs)
            return self.x[:, idx], self.y[idx]
        else:
            return self.x[idx], self.y[idx]


# ===== CUSTOM OPTIMIZERS =====

class SwarmOptimizer:
    """
    Custom Swarm Optimization algorithm for PyTorch models
    Implements particle swarm optimization with temperature control
    """

    def __init__(self, model, lr=0.1, ntrials=4, subtrials=50, temperature=0.0):
        """
        Initialize swarm optimizer
        model: PyTorch model to optimize
        lr: learning rate
        ntrials: number of particles in swarm
        subtrials: number of sub-iterations per particle
        temperature: temperature for stochastic acceptance
        """
        self.model = model
        self.lr = lr
        self.ntrials = ntrials
        self.subtrials = subtrials
        self.temperature = temperature

        # Initialize swarm
        self.swarm_params = []
        self.swarm_losses = []
        self.best_params = None
        self.best_loss = float('inf')

        # Get parameter shape
        with torch.no_grad():
            self.param_shape = model.params.shape

    def initialize_swarm(self, loss_fn, x, y):
        """Initialize swarm with random particles - matches original init_swarm_descent"""
        self.swarm_params = []
        self.swarm_losses = []

        for _ in range(self.ntrials):
            # Create random parameters following original pattern
            params = torch.zeros_like(self.model.params)
            layers, nfeatures = params.shape

            with torch.no_grad():
                # Match original initialization pattern exactly
                if layers >= 4:
                    # Original swarm initialization pattern
                    params[0, :] = torch.rand(nfeatures) * 0.50  # weights: [0.00, 0.50]
                    params[1, :] = torch.rand(nfeatures) * 0.75 + 0.25  # offsets: [0.25, 1.00]
                    params[2, :] = torch.rand(nfeatures) * 0.75 + 0.25  # skewness: [0.25, 1.00]
                    params[3, :] = torch.rand(nfeatures)  # centers: [0.00, 1.00]
                else:
                    # For other cases, use model-specific initialization
                    if hasattr(self.model, 'init_parameters'):
                        temp_model = type(self.model)(self.model.layers, self.model.nfeatures, self.model.args)
                        params.copy_(temp_model.params)
                    else:
                        params.uniform_(0, 1)

            self.swarm_params.append(params.clone())

            # Calculate initial loss
            self.model.params.data.copy_(params)
            with torch.no_grad():
                y_pred = self.model(x)
                loss = loss_fn(y_pred, y)
                self.swarm_losses.append(loss.item())

                if loss.item() < self.best_loss:
                    self.best_loss = loss.item()
                    self.best_params = params.clone()

        # Set model to best parameters
        self.model.params.data.copy_(self.best_params)

    def step(self, loss_fn, x, y, current_loss):
        """Perform one optimization step - matches original swarm_descent exactly"""
        with torch.no_grad():
            # Calculate normalization factor exactly as in original
            y_pred = self.model(x)
            norm = torch.sum(torch.abs(y_pred)) / len(y)

            # Track global best for this step
            best_global_loss = current_loss
            best_global_params = None

            for trial in range(self.ntrials):
                best_trial_loss = self.swarm_losses[trial]

                for _ in range(self.subtrials):
                    # Generate random perturbation - matches original exactly
                    perturbation = (torch.rand(self.param_shape) * 2 - 1) * norm

                    # Update parameters
                    test_params = self.swarm_params[trial] - self.lr * perturbation
                    test_params = torch.abs(test_params)  # Ensure positive parameters

                    # Evaluate new parameters
                    self.model.params.data.copy_(test_params)
                    y_pred = self.model(x)
                    new_loss = loss_fn(y_pred, y).item()

                    # Accept or reject based on loss and temperature - matches original
                    temp_factor = self.temperature
                    if new_loss < best_trial_loss or torch.rand(1).item() < temp_factor:
                        best_trial_loss = new_loss
                        self.swarm_params[trial] = test_params.clone()
                        self.swarm_losses[trial] = best_trial_loss

                # Update global best for this step - matches original logic
                if self.swarm_losses[trial] < best_global_loss:
                    best_global_loss = self.swarm_losses[trial]
                    best_global_params = self.swarm_params[trial].clone()

            # Update overall best if we found something better
            if best_global_params is not None:
                self.best_loss = best_global_loss
                self.best_params = best_global_params
                self.model.params.data.copy_(self.best_params)
            else:
                # Set model to current best parameters
                self.model.params.data.copy_(self.best_params)

class EnhancedGradientOptimizer:
    """
    Enhanced gradient descent with numerical derivatives and temperature control
    """

    def __init__(self, model, lr=0.1, eps=1e-6, temperature=0.0):
        """
        Initialize enhanced gradient optimizer
        model: PyTorch model to optimize
        lr: learning rate
        eps: epsilon for numerical differentiation
        temperature: temperature for stochastic perturbations
        """
        self.model = model
        self.lr = lr
        self.eps = eps
        self.temperature = temperature
        self.initialized = False

    def initialize_l2_descent(self, loss_fn, x, y):
        """Initialize L2 descent - matches original init_L2_descent"""
        with torch.no_grad():
            # Initialize all parameters to 0.5 as in original
            self.model.params.fill_(0.5)

            # Calculate initial loss
            y_pred = self.model(x)
            init_loss = loss_fn(y_pred, y).item()

            self.initialized = True
            return init_loss

    def step(self, loss_fn, x, y, current_loss):
        """Perform one optimization step using numerical gradients"""
        # Initialize if not done yet
        if not self.initialized:
            self.initialize_l2_descent(loss_fn, x, y)

        with torch.no_grad():
            layers, nfeatures = self.model.params.shape
            gradients = torch.zeros_like(self.model.params)

            # Calculate numerical gradients
            for l in range(layers):
                for k in range(nfeatures):
                    # Perturb parameter
                    original_value = self.model.params[l, k].item()
                    self.model.params[l, k] = original_value + self.eps

                    # Calculate loss with perturbed parameter
                    y_pred = self.model(x)
                    loss_right = loss_fn(y_pred, y).item()

                    # Calculate gradient
                    gradient = (loss_right - current_loss) / self.eps

                    # Apply temperature-based stochastic perturbation
                    if torch.rand(1).item() < self.temperature:
                        gradient = -gradient

                    gradients[l, k] = gradient

                    # Restore original parameter
                    self.model.params[l, k] = original_value

            # Update parameters
            new_params = self.model.params - self.lr * gradients

            # Apply parameter constraints - matches original logic exactly
            # Only update if current parameter is in valid range (0, 1) - exclusive bounds
            for l in range(layers):
                for k in range(nfeatures):
                    current_val = self.model.params[l, k].item()
                    if 0 < current_val < 1:
                        # Only update if the new value would also be in valid range
                        new_val = new_params[l, k].item()
                        if 0 <= new_val <= 1:  # Allow boundary values for new params
                            self.model.params[l, k] = new_params[l, k]


# ===== TRAINING FRAMEWORK =====

class DNNTrainer:
    """
    Comprehensive training framework for DNN models
    """

    def __init__(self, model, loss_fn, optimizer_type='L2', lr=0.1, temperature=0.0,
                 optimizer_args=None):
        """
        Initialize trainer
        model: PyTorch model to train
        loss_fn: loss function
        optimizer_type: 'L2', 'swarm_descent', or 'adam'
        lr: learning rate
        temperature: temperature for stochastic optimization
        optimizer_args: additional optimizer arguments
        """
        self.model = model
        self.loss_fn = loss_fn
        self.optimizer_type = optimizer_type
        self.lr = lr
        self.temperature = temperature
        self.optimizer_args = optimizer_args if optimizer_args else {}

        # Initialize optimizer
        self._init_optimizer()

        # Training history
        self.history = []
        self.best_loss = float('inf')
        self.best_params = None

    def _init_optimizer(self):
        """Initialize the appropriate optimizer"""
        if self.optimizer_type == 'swarm_descent':
            self.optimizer = SwarmOptimizer(
                self.model,
                lr=self.lr,
                temperature=self.temperature,
                **self.optimizer_args
            )
        elif self.optimizer_type == 'L2':
            self.optimizer = EnhancedGradientOptimizer(
                self.model,
                lr=self.lr,
                temperature=self.temperature,
                **self.optimizer_args
            )
        elif self.optimizer_type == 'adam':
            self.optimizer = optim.Adam(self.model.parameters(), lr=self.lr)
        else:
            raise ValueError(f"Unknown optimizer type: {self.optimizer_type}")

    def train(self, x, y, epochs=500, print_every=100, early_stopping=None):
        """
        Train the model
        x: input data tensor
        y: target data tensor
        epochs: number of training epochs
        print_every: print loss every N epochs
        early_stopping: early stopping patience (None to disable)
        """
        self.model.train()

        # Initialize for custom optimizers - matches original initialization patterns
        if hasattr(self.optimizer, 'initialize_swarm'):
            self.optimizer.initialize_swarm(self.loss_fn, x, y)
        elif hasattr(self.optimizer, 'initialize_l2_descent'):
            self.optimizer.initialize_l2_descent(self.loss_fn, x, y)

        # Initial loss
        with torch.no_grad():
            y_pred = self.model(x)
            initial_loss = self.loss_fn(y_pred, y).item()
            self.history.append(initial_loss)
            self.best_loss = initial_loss
            self.best_params = copy.deepcopy(self.model.state_dict())

        patience_counter = 0

        for epoch in range(epochs):
            if self.optimizer_type in ['swarm_descent', 'L2']:
                # Custom optimizers
                current_loss = self.history[-1]
                temp = self.temperature * current_loss  # Decay temperature

                if hasattr(self.optimizer, 'temperature'):
                    self.optimizer.temperature = temp

                self.optimizer.step(self.loss_fn, x, y, current_loss)

                # Calculate new loss
                with torch.no_grad():
                    y_pred = self.model(x)
                    loss = self.loss_fn(y_pred, y).item()

            else:
                # Standard PyTorch optimizer
                self.optimizer.zero_grad()
                y_pred = self.model(x)
                loss = self.loss_fn(y_pred, y)
                loss.backward()
                self.optimizer.step()
                loss = loss.item()

            self.history.append(loss)

            # Track best model
            if loss < self.best_loss:
                self.best_loss = loss
                self.best_params = copy.deepcopy(self.model.state_dict())
                patience_counter = 0
            else:
                patience_counter += 1

            # Print progress
            if epoch % print_every == 0:
                print(f"Epoch {epoch:5d} Loss {loss:8.5f}")

            # Early stopping
            if early_stopping and patience_counter >= early_stopping:
                print(f"Early stopping at epoch {epoch}")
                break

        # Load best model
        self.model.load_state_dict(self.best_params)

        return self.history

    def evaluate(self, x, y):
        """Evaluate model on given data"""
        self.model.eval()
        with torch.no_grad():
            y_pred = self.model(x)
            loss = self.loss_fn(y_pred, y).item()

            # Calculate correlation
            y_np = y.cpu().numpy()
            y_pred_np = y_pred.cpu().numpy()
            correlation = np.abs(np.corrcoef(y_np, y_pred_np)[0, 1])

            return {
                'loss': loss,
                'correlation': correlation,
                'predictions': y_pred
            }

    def save_checkpoint(self, filepath):
        """Save model checkpoint"""
        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'best_loss': self.best_loss,
            'history': self.history,
            'model_args': self.model.args,
            'model_type': type(self.model).__name__
        }
        torch.save(checkpoint, filepath)

    def load_checkpoint(self, filepath):
        """Load model checkpoint"""
        checkpoint = torch.load(filepath, map_location=device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.best_loss = checkpoint['best_loss']
        self.history = checkpoint['history']
        return checkpoint


# ===== EVALUATION AND TESTING FRAMEWORK =====

class DNNEvaluator:
    """
    Comprehensive evaluation framework for DNN models
    """

    def __init__(self, model, loss_fn):
        """
        Initialize evaluator
        model: trained PyTorch model
        loss_fn: loss function for evaluation
        """
        self.model = model
        self.loss_fn = loss_fn

    def evaluate_model(self, x, y, ground_truth_params=None):
        """
        Comprehensive model evaluation
        x: input data
        y: target data
        ground_truth_params: original parameters for comparison
        """
        self.model.eval()

        with torch.no_grad():
            # Get predictions
            y_pred = self.model(x)

            # Calculate metrics
            loss = self.loss_fn(y_pred, y).item()

            # Convert to numpy for correlation calculation
            y_np = y.cpu().numpy()
            y_pred_np = y_pred.cpu().numpy()

            # Calculate correlation
            correlation = np.abs(np.corrcoef(y_np, y_pred_np)[0, 1])

            # Calculate additional metrics
            mae = torch.mean(torch.abs(y_pred - y)).item()
            mse = torch.mean((y_pred - y)**2).item()
            rmse = np.sqrt(mse)

            # Calculate R-squared
            ss_res = torch.sum((y - y_pred)**2).item()
            ss_tot = torch.sum((y - torch.mean(y))**2).item()
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

            results = {
                'loss': loss,
                'mae': mae,
                'mse': mse,
                'rmse': rmse,
                'correlation': correlation,
                'r_squared': r_squared,
                'predictions': y_pred_np,
                'targets': y_np
            }

            # Parameter comparison if ground truth available
            if ground_truth_params is not None:
                estimated_params = self.model.get_numpy_params()
                param_diff = np.mean(np.abs(estimated_params - ground_truth_params))
                results['param_difference'] = param_diff

            return results

    def compare_with_original(self, x, y, original_function, original_params, args):
        """
        Compare PyTorch model with original NumPy implementation
        """
        # Get PyTorch predictions
        pytorch_results = self.evaluate_model(x, y)

        # Get original function predictions
        if isinstance(x, torch.Tensor):
            x_np = x.cpu().numpy()
        else:
            x_np = x

        if isinstance(y, torch.Tensor):
            y_np = y.cpu().numpy()
        else:
            y_np = y

        y_original = original_function(original_params, x_np, args)

        # Calculate original metrics
        original_mae = np.mean(np.abs(y_original - y_np))
        original_correlation = np.abs(np.corrcoef(y_original, y_np)[0, 1])

        comparison = {
            'pytorch_mae': pytorch_results['mae'],
            'original_mae': original_mae,
            'pytorch_correlation': pytorch_results['correlation'],
            'original_correlation': original_correlation,
            'mae_difference': abs(pytorch_results['mae'] - original_mae),
            'correlation_difference': abs(pytorch_results['correlation'] - original_correlation)
        }

        return comparison, pytorch_results

    def cross_validate(self, dataset, k_folds=5, seed=42):
        """
        Perform k-fold cross validation
        """
        torch.manual_seed(seed)
        np.random.seed(seed)

        # Get full dataset
        x_full = dataset.x
        y_full = dataset.y
        n_samples = len(y_full)

        # Create fold indices
        indices = torch.randperm(n_samples)
        fold_size = n_samples // k_folds

        fold_results = []

        for fold in range(k_folds):
            # Split data
            start_idx = fold * fold_size
            end_idx = start_idx + fold_size if fold < k_folds - 1 else n_samples

            test_indices = indices[start_idx:end_idx]
            train_indices = torch.cat([indices[:start_idx], indices[end_idx:]])

            # Create train/test splits
            if len(x_full.shape) == 2 and x_full.shape[0] != len(y_full):
                x_train, x_test = x_full[:, train_indices], x_full[:, test_indices]
            else:
                x_train, x_test = x_full[train_indices], x_full[test_indices]

            y_train, y_test = y_full[train_indices], y_full[test_indices]

            # Train model on fold
            trainer = DNNTrainer(
                copy.deepcopy(self.model),
                self.loss_fn,
                optimizer_type='L2'
            )
            trainer.train(x_train, y_train, epochs=100, print_every=1000)

            # Evaluate on test set
            results = trainer.evaluate(x_test, y_test)
            fold_results.append(results)

        # Aggregate results
        avg_loss = np.mean([r['loss'] for r in fold_results])
        avg_correlation = np.mean([r['correlation'] for r in fold_results])
        std_loss = np.std([r['loss'] for r in fold_results])
        std_correlation = np.std([r['correlation'] for r in fold_results])

        return {
            'avg_loss': avg_loss,
            'std_loss': std_loss,
            'avg_correlation': avg_correlation,
            'std_correlation': std_correlation,
            'fold_results': fold_results
        }


# ===== VISUALIZATION SYSTEM =====

class DNNVisualizer:
    """
    Comprehensive visualization system for DNN models
    """

    def __init__(self, model):
        """
        Initialize visualizer
        model: PyTorch model to visualize
        """
        self.model = model

    def plot_training_history(self, history, title="Training Loss", save_path=None):
        """Plot training loss history"""
        plt.figure(figsize=(10, 6))
        epochs = range(len(history))
        plt.plot(epochs, history, linewidth=0.6, c='gold', label='Training Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_predictions_vs_targets(self, y_true, y_pred, title="Predictions vs Targets", save_path=None):
        """Plot predictions against true values"""
        # Convert tensors to numpy if needed
        if isinstance(y_true, torch.Tensor):
            y_true = y_true.cpu().numpy()
        if isinstance(y_pred, torch.Tensor):
            y_pred = y_pred.cpu().numpy()

        plt.figure(figsize=(8, 8))
        plt.scatter(y_true, y_pred, s=0.8, c='gray', alpha=0.6)

        # Plot perfect prediction line
        min_val = min(np.min(y_true), np.min(y_pred))
        max_val = max(np.max(y_true), np.max(y_pred))
        plt.plot([min_val, max_val], [min_val, max_val], c='red', linewidth=0.8, alpha=1.0, label='Perfect Prediction')

        plt.xlabel('True Values')
        plt.ylabel('Predicted Values')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)

        # Calculate and display correlation
        correlation = np.corrcoef(y_true, y_pred)[0, 1]
        plt.text(0.05, 0.95, f'Correlation: {correlation:.4f}',
                transform=plt.gca().transAxes, bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_function_reconstruction_1d(self, x, y, x_fine=None, title="Function Reconstruction", save_path=None):
        """Plot 1D function reconstruction"""
        # Convert tensors to numpy if needed
        if isinstance(x, torch.Tensor):
            x = x.cpu().numpy()
        if isinstance(y, torch.Tensor):
            y = y.cpu().numpy()

        self.model.eval()

        # Create fine-grained x values for smooth curve
        if x_fine is None:
            x_fine = np.linspace(np.min(x), np.max(x), 1000)

        # Get model predictions on fine grid
        with torch.no_grad():
            x_fine_tensor = torch.tensor(x_fine, dtype=torch.float32)
            y_fine_pred = self.model(x_fine_tensor).cpu().numpy()

        plt.figure(figsize=(12, 6))

        # Plot original data points
        plt.scatter(x, y, c='gold', s=1.5, alpha=0.8, label='Training Data')

        # Plot reconstructed function
        plt.plot(x_fine, y_fine_pred, c='green', linewidth=0.8, alpha=0.8, label='Reconstructed Function')

        plt.xlabel('Input')
        plt.ylabel('Output')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_function_comparison_1d(self, x, y_true, y_pred, ground_truth_func=None,
                                   ground_truth_params=None, args=None, title="Function Comparison", save_path=None):
        """Compare reconstructed function with ground truth"""
        # Convert tensors to numpy if needed
        if isinstance(x, torch.Tensor):
            x = x.cpu().numpy()
        if isinstance(y_true, torch.Tensor):
            y_true = y_true.cpu().numpy()
        if isinstance(y_pred, torch.Tensor):
            y_pred = y_pred.cpu().numpy()

        # Create fine-grained x values
        x_fine = np.linspace(np.min(x), np.max(x), 1000)

        plt.figure(figsize=(12, 6))

        # Plot original data points
        plt.scatter(x, y_true, c='gold', s=1.5, alpha=0.8, label='Training Data')

        # Plot reconstructed function
        self.model.eval()
        with torch.no_grad():
            x_fine_tensor = torch.tensor(x_fine, dtype=torch.float32)
            y_fine_pred = self.model(x_fine_tensor).cpu().numpy()
        plt.plot(x_fine, y_fine_pred, c='green', linewidth=0.8, alpha=0.8, label='Reconstructed Function')

        # Plot ground truth function if available
        if ground_truth_func and ground_truth_params is not None:
            y_fine_true = ground_truth_func(ground_truth_params, x_fine, args if args else {})
            plt.plot(x_fine, y_fine_true, c='red', linewidth=0.8, alpha=0.8, label='Ground Truth Function')

        plt.xlabel('Input')
        plt.ylabel('Output')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_eta_orbit(self, x, y, real_net, imag_net, ground_truth_params=None,
                      args=None, title="Dirichlet Eta Orbit", save_path=None):
        """Plot complex orbit for Dirichlet eta function"""
        # Convert tensors to numpy if needed
        if isinstance(x, torch.Tensor):
            x = x.cpu().numpy()
        if isinstance(y, torch.Tensor):
            y = y.cpu().numpy()

        # Create fine-grained x values
        x_min, x_max = np.min(x), np.max(x)
        step = (x_max - x_min) / 5000
        x_fine = np.arange(x_min, x_max, step)

        real_net.eval()
        imag_net.eval()

        with torch.no_grad():
            x_fine_tensor = torch.tensor(x_fine, dtype=torch.float32)

            # Get reconstructed real and imaginary parts
            y_real_recon = real_net(x_fine_tensor).cpu().numpy()
            y_imag_recon = imag_net(x_fine_tensor).cpu().numpy()

        plt.figure(figsize=(10, 10))

        # Plot reconstructed orbit
        plt.scatter(y_real_recon, y_imag_recon, s=0.08, c='green', alpha=0.6, label='Reconstructed Orbit')

        # Plot ground truth orbit if available
        if ground_truth_params is not None and args is not None:
            # Import original functions for comparison
            from dnn import f2, f3
            y_real_exact = f2(ground_truth_params, x_fine, args)
            y_imag_exact = f3(ground_truth_params, x_fine, args)

            plt.scatter(y_real_exact, y_imag_exact, s=0.08, c='red', alpha=0.6, label='Ground Truth Orbit')

            # Draw connecting lines between exact and reconstructed points
            for k in range(0, len(x_fine), 5):
                exact = (y_real_exact[k], y_imag_exact[k])
                reconstructed = (y_real_recon[k], y_imag_recon[k])
                plt.plot([exact[0], reconstructed[0]], [exact[1], reconstructed[1]],
                        c='gold', linewidth=0.4, alpha=0.5)

        plt.xlabel('Real Part')
        plt.ylabel('Imaginary Part')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axis('equal')

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()


# ===== CONFIGURATION MANAGEMENT =====

class DNNConfig:
    """Configuration management for DNN experiments"""

    def __init__(self):
        """Initialize default configuration"""
        self.config = {
            # General settings
            'seed': 565,
            'device': 'cpu',

            # Data settings
            'n_observations': 300,
            'nfeatures': 10,
            'distill_rate': 1.0,
            'noise_alpha': 0.10,

            # Training settings
            'epochs': 501,
            'learning_rate': 0.1,
            'temperature': 0.0,
            'print_every': 100,
            'early_stopping': None,

            # Loss and optimization
            'loss_type': 'L2',  # 'L1_abs', 'L1_avg', 'L2'
            'optimizer_type': 'L2',  # 'L2', 'swarm_descent', 'adam'
            'eps': 1e-6,  # for numerical gradients

            # Swarm optimization specific
            'ntrials': 4,
            'subtrials': 50,

            # Model specific settings
            'layers': 3,
            'function_type': 'f2',  # 'f0', 'f1', 'f2'

            # Function-specific arguments
            'f0_args': {
                'model': 'gaussian',  # 'gaussian' or 'approx. gaussian'
                'equalize': True,
                'centers': 5
            },
            'f1_args': {
                'small': 0.001,
                'function_type': 'rational',  # 'rational' or 'polynom'
                'model': 'latent',  # 'latent' or 'static'
                'equalize': False
            },
            'f2_args': {
                'equalize': True
            }
        }

    def update(self, **kwargs):
        """Update configuration with new values"""
        self.config.update(kwargs)

    def get(self, key, default=None):
        """Get configuration value"""
        return self.config.get(key, default)

    def get_function_args(self, function_type):
        """Get function-specific arguments"""
        return self.config.get(f'{function_type}_args', {})

    def set_function_config(self, function_type):
        """Set configuration for specific function type"""
        if function_type == 'f0':
            self.config.update({
                'nfeatures': 7,
                'layers': 4 * self.config['f0_args']['centers'],
                'distill_rate': 0.5
            })
        elif function_type == 'f1':
            self.config.update({
                'nfeatures': 12,
                'layers': 4,
                'optimizer_type': 'swarm_descent'
            })
        elif function_type == 'f2':
            self.config.update({
                'nfeatures': 30,
                'layers': 3
            })

    def save_config(self, filepath):
        """Save configuration to file"""
        import json
        with open(filepath, 'w') as f:
            json.dump(self.config, f, indent=2)

    def load_config(self, filepath):
        """Load configuration from file"""
        import json
        with open(filepath, 'r') as f:
            self.config.update(json.load(f))


# ===== DATA GENERATION UTILITIES =====

def generate_data(function_type, config):
    """
    Generate training data for specified function type
    """
    torch.manual_seed(config.get('seed', 565))
    np.random.seed(config.get('seed', 565))

    n = config.get('n_observations', 300)
    nfeatures = config.get('nfeatures', 10)
    layers = config.get('layers', 3)
    noise_alpha = config.get('noise_alpha', 0.1)

    # Get function-specific arguments
    args = config.get_function_args(function_type)

    if function_type == 'f0':
        # Multivariate Gaussian function
        params = np.random.uniform(0, 1, (layers, nfeatures))
        x = np.random.uniform(0, 1, (nfeatures, n))

        # Create model and generate data
        model = GaussianNet(layers, nfeatures, args)
        model.set_params_from_numpy(params)

        with torch.no_grad():
            x_tensor = torch.tensor(x, dtype=torch.float32)
            y_base = model(x_tensor).numpy()

    elif function_type == 'f1':
        # Rational function
        params = np.zeros((layers, nfeatures))
        params[0, :] = np.random.uniform(0.25, 0.50, nfeatures)  # weights
        params[1, :] = np.random.uniform(0.25, 1.00, nfeatures)  # offset
        params[2, :] = np.random.uniform(0.25, 1.00, nfeatures)  # skewness
        params[3, :] = np.random.uniform(0.00, 1.00, nfeatures)  # centers
        params[0, :4] = [0, 0, 0, 0]  # first 4 features not used

        x = np.linspace(0, 1, num=n)
        args['centers'] = params[3, :]  # used as fixed centers in static model

        # Create model and generate data
        model = RationalNet(layers, nfeatures, args)
        model.set_params_from_numpy(params)

        with torch.no_grad():
            x_tensor = torch.tensor(x, dtype=torch.float32)
            y_base = model(x_tensor).numpy()

    elif function_type == 'f2':
        # Dirichlet eta function (real part)
        params = np.zeros((layers, nfeatures))
        params[0, 0] = 0.75  # sigma
        params[1, 0] = 0.10  # phi
        for k in range(nfeatures):
            params[2, k] = 1.0  # coefficients

        x = np.random.uniform(0, 25, n)

        # Create model and generate data
        model = DirichletNet(layers, nfeatures, args, part='real')
        model.set_params_from_numpy(params)

        with torch.no_grad():
            x_tensor = torch.tensor(x, dtype=torch.float32)
            y_base = model(x_tensor).numpy()

    else:
        raise ValueError(f"Unknown function type: {function_type}")

    # Add noise
    y = y_base.copy()
    stdev_y = np.std(y)
    y += np.random.normal(0, noise_alpha * stdev_y, len(y))

    return x, y, y_base, params, args


# ===== MAIN EXECUTION FUNCTIONS =====

def create_model(function_type, layers, nfeatures, args):
    """Create appropriate model based on function type"""
    if function_type == 'f0':
        return GaussianNet(layers, nfeatures, args)
    elif function_type == 'f1':
        return RationalNet(layers, nfeatures, args)
    elif function_type == 'f2':
        return DirichletNet(layers, nfeatures, args, part='real')
    elif function_type == 'f3':
        return DirichletNet(layers, nfeatures, args, part='imaginary')
    elif function_type == 'g1':
        return ReparameterizedRationalNet(layers, nfeatures, args)
    else:
        raise ValueError(f"Unknown function type: {function_type}")

def create_loss_function(loss_type):
    """Create appropriate loss function"""
    if loss_type == 'L1_abs':
        return L1AbsLoss()
    elif loss_type == 'L1_avg':
        return L1AvgLoss()
    elif loss_type == 'L2':
        return L2Loss()
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")

def run_experiment(function_type='f2', config=None, verbose=True):
    """
    Run a complete DNN experiment
    """
    if config is None:
        config = DNNConfig()

    # Set function-specific configuration
    config.set_function_config(function_type)

    if verbose:
        print(f"Running experiment with function type: {function_type}")
        print(f"Configuration: {config.config}")

    # Set random seeds
    torch.manual_seed(config.get('seed'))
    np.random.seed(config.get('seed'))

    # Generate data
    x, y, y_base, ground_truth_params, args = generate_data(function_type, config)

    # Create dataset with distillation
    dataset = DNNDataset(x, y, distill_rate=config.get('distill_rate'), seed=config.get('seed'))

    # Create model
    model = create_model(function_type, config.get('layers'), config.get('nfeatures'), args)

    # Create loss function
    loss_fn = create_loss_function(config.get('loss_type'))

    # Create trainer
    optimizer_args = {}
    if config.get('optimizer_type') == 'swarm_descent':
        optimizer_args = {
            'ntrials': config.get('ntrials'),
            'subtrials': config.get('subtrials')
        }
    elif config.get('optimizer_type') == 'L2':
        optimizer_args = {
            'eps': config.get('eps')
        }

    trainer = DNNTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer_type=config.get('optimizer_type'),
        lr=config.get('learning_rate'),
        temperature=config.get('temperature'),
        optimizer_args=optimizer_args
    )

    # Train model
    if verbose:
        print("Starting training...")

    # Get training data from dataset
    x_train = dataset.x
    y_train = dataset.y

    history = trainer.train(
        x_train, y_train,
        epochs=config.get('epochs'),
        print_every=config.get('print_every'),
        early_stopping=config.get('early_stopping')
    )

    # Evaluate model
    evaluator = DNNEvaluator(model, loss_fn)

    # Convert full data to tensors for evaluation
    x_full = torch.tensor(x, dtype=torch.float32) if isinstance(x, np.ndarray) else x
    y_full = torch.tensor(y, dtype=torch.float32) if isinstance(y, np.ndarray) else y

    results = evaluator.evaluate_model(x_full, y_full, ground_truth_params)

    if verbose:
        print(f"\nTraining completed!")
        print(f"Final Loss: {results['loss']:.6f}")
        print(f"MAE: {results['mae']:.6f}")
        print(f"RMSE: {results['rmse']:.6f}")
        print(f"Correlation: {results['correlation']:.6f}")
        print(f"R-squared: {results['r_squared']:.6f}")

    # Visualization
    visualizer = DNNVisualizer(model)

    # Plot training history
    visualizer.plot_training_history(history, title=f"Training History - {function_type}")

    # Plot predictions vs targets
    visualizer.plot_predictions_vs_targets(
        results['targets'], results['predictions'],
        title=f"Predictions vs Targets - {function_type}"
    )

    # Function-specific visualizations
    if function_type in ['f1', 'g1'] and len(x.shape) == 1:
        # 1D function reconstruction
        visualizer.plot_function_reconstruction_1d(
            x_train, y_train,
            title=f"Function Reconstruction - {function_type}"
        )

    return {
        'model': model,
        'trainer': trainer,
        'evaluator': evaluator,
        'visualizer': visualizer,
        'results': results,
        'history': history,
        'config': config,
        'ground_truth_params': ground_truth_params,
        'args': args
    }

def compare_with_original_implementation(function_type='f2', config=None):
    """
    Compare PyTorch implementation with original NumPy implementation
    """
    if config is None:
        config = DNNConfig()

    # Import original functions
    try:
        from dnn import f0, f1, f2, f3, g1
        original_functions = {'f0': f0, 'f1': f1, 'f2': f2, 'f3': f3, 'g1': g1}
    except ImportError:
        print("Warning: Could not import original functions from dnn.py")
        return None

    # Run PyTorch experiment
    experiment_results = run_experiment(function_type, config, verbose=False)

    # Get original function
    original_func = original_functions.get(function_type)
    if original_func is None:
        print(f"Original function {function_type} not found")
        return None

    # Compare implementations
    evaluator = experiment_results['evaluator']
    x = experiment_results['trainer'].model.args.get('x_data', None)
    y = experiment_results['trainer'].model.args.get('y_data', None)

    if x is not None and y is not None:
        comparison = evaluator.compare_with_original(
            x, y, original_func,
            experiment_results['ground_truth_params'],
            experiment_results['args']
        )

        print("Comparison with original implementation:")
        print(f"PyTorch MAE: {comparison[1]['mae']:.6f}")
        print(f"Original MAE: {comparison[0]['original_mae']:.6f}")
        print(f"MAE Difference: {comparison[0]['mae_difference']:.6f}")
        print(f"PyTorch Correlation: {comparison[1]['correlation']:.6f}")
        print(f"Original Correlation: {comparison[0]['original_correlation']:.6f}")
        print(f"Correlation Difference: {comparison[0]['correlation_difference']:.6f}")

        return comparison

    return None


def demo_all_functions():
    """
    Demonstrate all function types with different configurations
    """
    print("=" * 60)
    print("PyTorch DNN Framework - Complete Demonstration")
    print("=" * 60)

    function_types = ['f0', 'f1', 'f2']
    results = {}

    for func_type in function_types:
        print(f"\n{'='*20} Testing {func_type} {'='*20}")

        # Create configuration for this function
        config = DNNConfig()
        config.update(epochs=200, print_every=50)  # Shorter training for demo

        try:
            # Run experiment
            experiment_results = run_experiment(func_type, config, verbose=True)
            results[func_type] = experiment_results

            print(f"✓ {func_type} experiment completed successfully")

        except Exception as e:
            print(f"✗ {func_type} experiment failed: {str(e)}")
            results[func_type] = None

    print("\n" + "=" * 60)
    print("Summary of Results:")
    print("=" * 60)

    for func_type, result in results.items():
        if result is not None:
            final_loss = result['results']['loss']
            correlation = result['results']['correlation']
            print(f"{func_type}: Loss={final_loss:.6f}, Correlation={correlation:.6f}")
        else:
            print(f"{func_type}: Failed")

    return results

def test_reparameterization():
    """
    Test reparameterization functionality
    """
    print("\n" + "=" * 40)
    print("Testing Reparameterization")
    print("=" * 40)

    # Create test data
    config = DNNConfig()
    config.set_function_config('f1')

    x, y, y_base, params, args = generate_data('f1', config)
    x_tensor = torch.tensor(x, dtype=torch.float32)

    # Create original and reparameterized networks
    original_net = RationalNet(config.get('layers'), config.get('nfeatures'), args)
    reparam_net = ReparameterizedRationalNet(config.get('layers'), config.get('nfeatures'), args)

    # Set parameters
    original_net.set_params_from_numpy(params)

    # Test reparameterization
    is_correct = check_reparameterization(original_net, reparam_net, x_tensor)

    if is_correct:
        print("✓ Reparameterization test passed!")
    else:
        print("✗ Reparameterization test failed!")

    return is_correct

def benchmark_optimizers():
    """
    Benchmark different optimizers on the same problem
    """
    print("\n" + "=" * 40)
    print("Benchmarking Optimizers")
    print("=" * 40)

    optimizers = ['L2', 'swarm_descent', 'adam']
    function_type = 'f1'  # Use f1 for benchmarking

    results = {}

    for opt_type in optimizers:
        print(f"\nTesting optimizer: {opt_type}")

        config = DNNConfig()
        config.update(
            optimizer_type=opt_type,
            epochs=100,
            print_every=1000  # Suppress output
        )
        config.set_function_config(function_type)

        try:
            experiment_results = run_experiment(function_type, config, verbose=False)
            results[opt_type] = {
                'final_loss': experiment_results['results']['loss'],
                'correlation': experiment_results['results']['correlation'],
                'training_time': len(experiment_results['history'])
            }
            print(f"✓ {opt_type}: Loss={results[opt_type]['final_loss']:.6f}, "
                  f"Correlation={results[opt_type]['correlation']:.6f}")

        except Exception as e:
            print(f"✗ {opt_type} failed: {str(e)}")
            results[opt_type] = None

    return results


# ===== MAIN EXECUTION =====

if __name__ == "__main__":
    print("PyTorch Deep Neural Network Framework")
    print("Converted from NumPy implementation")
    print("=" * 50)

    # Set random seed for reproducibility
    torch.manual_seed(565)
    np.random.seed(565)

    # Run demonstrations
    try:
        # Demo all function types
        demo_results = demo_all_functions()

        # Test reparameterization
        reparam_test = test_reparameterization()

        # Benchmark optimizers
        optimizer_benchmark = benchmark_optimizers()

        print("\n" + "=" * 50)
        print("All demonstrations completed successfully!")
        print("=" * 50)

        # Example of how to use the framework for custom experiments
        print("\nExample: Custom experiment with f2 function")
        config = DNNConfig()
        config.update(
            function_type='f2',
            epochs=300,
            learning_rate=0.05,
            temperature=0.1
        )

        custom_results = run_experiment('f2', config)
        print(f"Custom experiment completed with final loss: {custom_results['results']['loss']:.6f}")

    except Exception as e:
        print(f"Error during execution: {str(e)}")
        import traceback
        traceback.print_exc()
