#!/usr/bin/env python3
"""
Test script to validate the improvements made to the PyTorch implementation
"""

import torch
import numpy as np
from pytorch_dnn import (
    SwarmOptimizer, EnhancedGradientOptimizer, DNNTrainer,
    RationalNet, DirichletNet, L2Loss, DNNConfig
)

def test_swarm_initialization_improvement():
    """Test that swarm initialization follows original pattern"""
    print("Testing improved swarm initialization...")
    
    # Create a model for testing
    config = DNNConfig()
    config.set_function_config('f1')
    
    model = RationalNet(4, 12, config.get_function_args('f1'))
    loss_fn = L2Loss()
    
    # Create swarm optimizer
    swarm_opt = SwarmOptimizer(model, ntrials=3, subtrials=10)
    
    # Generate test data
    x = torch.linspace(0, 1, 100)
    y = torch.sin(x * 3.14159)
    
    # Initialize swarm
    swarm_opt.initialize_swarm(loss_fn, x, y)
    
    # Check that initialization follows the correct pattern
    for i, params in enumerate(swarm_opt.swarm_params):
        layers, nfeatures = params.shape
        if layers >= 4:
            # Check weight range [0.00, 0.50]
            weights = params[0, :]
            assert torch.all(weights >= 0.0) and torch.all(weights <= 0.50), f"Trial {i}: weights out of range"
            
            # Check offset range [0.25, 1.00]
            offsets = params[1, :]
            assert torch.all(offsets >= 0.25) and torch.all(offsets <= 1.00), f"Trial {i}: offsets out of range"
            
            # Check skewness range [0.25, 1.00]
            skewness = params[2, :]
            assert torch.all(skewness >= 0.25) and torch.all(skewness <= 1.00), f"Trial {i}: skewness out of range"
            
            # Check centers range [0.00, 1.00]
            centers = params[3, :]
            assert torch.all(centers >= 0.0) and torch.all(centers <= 1.00), f"Trial {i}: centers out of range"
    
    print("✓ Swarm initialization improvement validated")
    return True

def test_l2_initialization_improvement():
    """Test that L2 initialization sets all parameters to 0.5"""
    print("Testing improved L2 initialization...")
    
    # Create a model for testing
    config = DNNConfig()
    config.set_function_config('f2')
    
    model = DirichletNet(3, 10, config.get_function_args('f2'))
    loss_fn = L2Loss()
    
    # Create L2 optimizer
    l2_opt = EnhancedGradientOptimizer(model, lr=0.1, eps=1e-6)
    
    # Generate test data
    x = torch.linspace(0, 10, 100)
    y = torch.sin(x)
    
    # Initialize L2 descent
    init_loss = l2_opt.initialize_l2_descent(loss_fn, x, y)
    
    # Check that all parameters are set to 0.5
    params = model.params.detach().numpy()
    expected_params = np.full_like(params, 0.5)
    
    diff = np.max(np.abs(params - expected_params))
    assert diff < 1e-10, f"L2 initialization failed: max diff = {diff}"
    
    print(f"✓ L2 initialization improvement validated (init_loss = {init_loss:.6f})")
    return True

def test_parameter_constraint_improvement():
    """Test that parameter constraints work correctly"""
    print("Testing improved parameter constraints...")

    # Create a model for testing
    config = DNNConfig()
    config.set_function_config('f2')

    model = DirichletNet(3, 5, config.get_function_args('f2'))
    loss_fn = L2Loss()

    # Create L2 optimizer but don't let it auto-initialize
    l2_opt = EnhancedGradientOptimizer(model, lr=0.1, eps=1e-6)
    l2_opt.initialized = True  # Prevent auto-initialization

    # Generate test data
    x = torch.linspace(0, 5, 50)
    y = torch.sin(x)

    # Set some parameters outside valid range to test constraints
    with torch.no_grad():
        model.params[0, 0] = -0.1  # Outside range
        model.params[1, 0] = 1.5   # Outside range
        model.params[2, 0] = 0.5   # Inside range
        model.params[2, 1] = 0.3   # Inside range

    # Store original values
    original_params = model.params.clone()

    # Perform one optimization step
    current_loss = loss_fn(model(x), y).item()
    l2_opt.step(loss_fn, x, y, current_loss)

    # Check that only parameters in valid range were updated
    new_params = model.params

    # Parameters outside range should not be updated
    diff_0_0 = torch.abs(new_params[0, 0] - original_params[0, 0]).item()
    diff_1_0 = torch.abs(new_params[1, 0] - original_params[1, 0]).item()

    # These should be unchanged (outside valid range)
    assert diff_0_0 < 1e-10, f"Out-of-range parameter [0,0] was updated: {diff_0_0}"
    assert diff_1_0 < 1e-10, f"Out-of-range parameter [1,0] was updated: {diff_1_0}"

    # Parameters inside range could be updated
    diff_2_0 = torch.abs(new_params[2, 0] - original_params[2, 0]).item()
    diff_2_1 = torch.abs(new_params[2, 1] - original_params[2, 1]).item()

    print(f"✓ Out-of-range parameters preserved: [0,0]={diff_0_0:.2e}, [1,0]={diff_1_0:.2e}")
    print(f"✓ In-range parameters could update: [2,0]={diff_2_0:.2e}, [2,1]={diff_2_1:.2e}")
    print(f"✓ Parameter constraint improvement validated")
    return True

def test_training_integration():
    """Test that all improvements work together in training"""
    print("Testing integrated training with improvements...")
    
    # Create configuration
    config = DNNConfig()
    config.update(epochs=20, print_every=1000)
    config.set_function_config('f2')
    
    # Generate data
    np.random.seed(565)
    torch.manual_seed(565)
    
    x = np.random.uniform(0, 5, 50)
    y = np.sin(x) + np.random.normal(0, 0.01, 50)
    
    # Test with L2 optimizer
    model_l2 = DirichletNet(3, 10, config.get_function_args('f2'))
    loss_fn = L2Loss()
    
    trainer_l2 = DNNTrainer(
        model=model_l2,
        loss_fn=loss_fn,
        optimizer_type='L2',
        lr=0.1,
        temperature=0.0
    )
    
    x_tensor = torch.tensor(x, dtype=torch.float32)
    y_tensor = torch.tensor(y, dtype=torch.float32)
    
    history_l2 = trainer_l2.train(x_tensor, y_tensor, epochs=20, print_every=1000)
    
    # Test with swarm optimizer
    model_swarm = RationalNet(4, 8, config.get_function_args('f1'))
    
    trainer_swarm = DNNTrainer(
        model=model_swarm,
        loss_fn=loss_fn,
        optimizer_type='swarm_descent',
        lr=0.1,
        temperature=0.0,
        optimizer_args={'ntrials': 3, 'subtrials': 10}
    )
    
    history_swarm = trainer_swarm.train(x_tensor, y_tensor, epochs=10, print_every=1000)
    
    # Check that training worked
    assert len(history_l2) == 21, f"L2 training history wrong length: {len(history_l2)}"
    assert len(history_swarm) == 11, f"Swarm training history wrong length: {len(history_swarm)}"
    
    # Check that loss decreased or stayed stable
    l2_improvement = (history_l2[0] - history_l2[-1]) / history_l2[0]
    swarm_improvement = (history_swarm[0] - history_swarm[-1]) / history_swarm[0]
    
    print(f"✓ L2 training: {l2_improvement:.1%} improvement")
    print(f"✓ Swarm training: {swarm_improvement:.1%} improvement")
    print("✓ Integrated training with improvements validated")
    
    return True

def run_improvement_tests():
    """Run all improvement validation tests"""
    print("="*80)
    print("PYTORCH DNN IMPROVEMENTS VALIDATION")
    print("="*80)
    
    tests = [
        test_swarm_initialization_improvement,
        test_l2_initialization_improvement,
        test_parameter_constraint_improvement,
        test_training_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
            print()
        except Exception as e:
            print(f"✗ {test_func.__name__} failed: {str(e)}")
            print()
    
    print("="*80)
    print(f"IMPROVEMENT VALIDATION RESULTS: {passed}/{total} tests passed")
    print("="*80)
    
    if passed == total:
        print("🎉 ALL IMPROVEMENTS VALIDATED SUCCESSFULLY!")
        print("The PyTorch implementation now has enhanced fidelity to the original.")
    else:
        print("⚠️ Some improvement tests failed. Please review the results above.")
    
    return passed == total

if __name__ == "__main__":
    # Set random seeds for reproducibility
    torch.manual_seed(565)
    np.random.seed(565)
    
    success = run_improvement_tests()
    
    if success:
        print("\n✅ All improvements successfully implemented and validated!")
    else:
        print("\n❌ Some improvements need attention.")
