#!/usr/bin/env python3
"""
Optimization Comparison Test
Compares training convergence between PyTorch and NumPy implementations
"""

import torch
import numpy as np
import time
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

# Import original NumPy implementation
try:
    from dnn import (
        f2 as numpy_f2, gradient_descent as numpy_gradient_descent,
        loss as numpy_loss
    )
    NUMPY_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import original NumPy implementation: {e}")
    NUMPY_AVAILABLE = False

# Import PyTorch implementation
from pytorch_dnn import (
    DirichletNet, DNNTrainer, L2Loss, generate_data, DNNConfig
)

def create_identical_test_data(seed=565, n_obs=200, noise_level=0.05):
    """Create identical test data for both implementations"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    
    # Parameters for f2 (Dirichlet function)
    nfeatures = 15  # Smaller for faster testing
    layers = 3
    
    # Ground truth parameters
    params_true = np.zeros((layers, nfeatures))
    params_true[0, 0] = 0.75  # sigma
    params_true[1, 0] = 0.10  # phi
    for k in range(nfeatures):
        params_true[2, k] = 1.0  # coefficients
    
    # Generate input data
    x = np.random.uniform(0, 15, n_obs)  # Smaller range for stability
    
    # Generate clean output
    args = {'equalize': True}
    y_clean = numpy_f2(params_true, x, args)
    
    # Add noise
    noise = np.random.normal(0, noise_level * np.std(y_clean), n_obs)
    y_noisy = y_clean + noise
    
    return x, y_noisy, y_clean, params_true, args, nfeatures, layers

def test_numpy_optimization(x, y, params_true, args, nfeatures, layers, epochs=100):
    """Test NumPy optimization"""
    print("Testing NumPy optimization...")
    
    # Set seed for reproducible initialization
    np.random.seed(565)
    
    # Prepare arguments for NumPy implementation
    args_numpy = args.copy()
    args_numpy['eps'] = 1e-6
    
    start_time = time.time()
    
    try:
        params_estimated, history = numpy_gradient_descent(
            epochs=epochs,
            learning_rate=0.1,
            layers=layers,
            nfeatures=nfeatures,
            f=numpy_f2,
            y=y,
            x=x,
            temperature=0.0,
            L_error='L2',
            descent='L2',
            args=args_numpy
        )
        
        training_time = time.time() - start_time
        final_loss = history[-1]
        
        # Calculate final predictions
        y_pred = numpy_f2(params_estimated, x, args)
        correlation = np.abs(np.corrcoef(y, y_pred)[0, 1])
        
        success = True
        
    except Exception as e:
        print(f"NumPy optimization failed: {e}")
        success = False
        params_estimated = None
        history = []
        training_time = float('inf')
        final_loss = float('inf')
        correlation = 0.0
    
    return {
        'success': success,
        'params': params_estimated,
        'history': history,
        'training_time': training_time,
        'final_loss': final_loss,
        'correlation': correlation
    }

def test_pytorch_optimization(x, y, params_true, args, nfeatures, layers, epochs=100):
    """Test PyTorch optimization"""
    print("Testing PyTorch optimization...")
    
    # Set seed for reproducible initialization
    torch.manual_seed(565)
    np.random.seed(565)
    
    # Create model
    model = DirichletNet(layers, nfeatures, args, part='real')
    loss_fn = L2Loss()
    
    # Create trainer
    trainer = DNNTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer_type='L2',
        lr=0.1,
        temperature=0.0,
        optimizer_args={'eps': 1e-6}
    )
    
    # Convert data to tensors
    x_tensor = torch.tensor(x, dtype=torch.float32)
    y_tensor = torch.tensor(y, dtype=torch.float32)
    
    start_time = time.time()
    
    try:
        # Train
        history = trainer.train(x_tensor, y_tensor, epochs=epochs, print_every=1000)
        
        training_time = time.time() - start_time
        final_loss = history[-1]
        
        # Calculate final predictions
        model.eval()
        with torch.no_grad():
            y_pred = model(x_tensor).numpy()
        
        correlation = np.abs(np.corrcoef(y, y_pred)[0, 1])
        params_estimated = model.get_numpy_params()
        
        success = True
        
    except Exception as e:
        print(f"PyTorch optimization failed: {e}")
        success = False
        params_estimated = None
        history = []
        training_time = float('inf')
        final_loss = float('inf')
        correlation = 0.0
    
    return {
        'success': success,
        'params': params_estimated,
        'history': history,
        'training_time': training_time,
        'final_loss': final_loss,
        'correlation': correlation
    }

def compare_optimization_results(numpy_result, pytorch_result):
    """Compare optimization results between implementations"""
    print("\n" + "="*60)
    print("OPTIMIZATION COMPARISON RESULTS")
    print("="*60)
    
    # Success comparison
    print(f"NumPy Success: {'✓' if numpy_result['success'] else '✗'}")
    print(f"PyTorch Success: {'✓' if pytorch_result['success'] else '✗'}")
    
    if not (numpy_result['success'] and pytorch_result['success']):
        print("⚠️  Cannot compare results - one or both optimizations failed")
        return None
    
    # Performance comparison
    print(f"\nTraining Time:")
    print(f"  NumPy: {numpy_result['training_time']:.3f}s")
    print(f"  PyTorch: {pytorch_result['training_time']:.3f}s")
    speedup = numpy_result['training_time'] / pytorch_result['training_time']
    print(f"  Speedup: {speedup:.2f}x {'(PyTorch faster)' if speedup > 1 else '(NumPy faster)'}")
    
    # Convergence comparison
    print(f"\nFinal Loss:")
    print(f"  NumPy: {numpy_result['final_loss']:.6f}")
    print(f"  PyTorch: {pytorch_result['final_loss']:.6f}")
    loss_ratio = abs(numpy_result['final_loss'] - pytorch_result['final_loss']) / min(numpy_result['final_loss'], pytorch_result['final_loss'])
    print(f"  Relative difference: {loss_ratio:.2%}")
    
    # Correlation comparison
    print(f"\nCorrelation:")
    print(f"  NumPy: {numpy_result['correlation']:.6f}")
    print(f"  PyTorch: {pytorch_result['correlation']:.6f}")
    corr_diff = abs(numpy_result['correlation'] - pytorch_result['correlation'])
    print(f"  Absolute difference: {corr_diff:.6f}")
    
    # Convergence behavior
    print(f"\nConvergence:")
    numpy_epochs = len(numpy_result['history'])
    pytorch_epochs = len(pytorch_result['history'])
    print(f"  NumPy epochs: {numpy_epochs}")
    print(f"  PyTorch epochs: {pytorch_epochs}")
    
    # Assessment
    convergence_similar = loss_ratio < 0.2  # 20% tolerance
    correlation_similar = corr_diff < 0.05   # 5% tolerance
    
    if convergence_similar and correlation_similar:
        assessment = "✅ EXCELLENT - Both implementations converge to similar solutions"
    elif convergence_similar or correlation_similar:
        assessment = "⚠️  GOOD - Implementations show similar convergence behavior"
    else:
        assessment = "❌ POOR - Significant differences in convergence"
    
    print(f"\nAssessment: {assessment}")
    
    return {
        'speedup': speedup,
        'loss_ratio': loss_ratio,
        'correlation_diff': corr_diff,
        'convergence_similar': convergence_similar,
        'correlation_similar': correlation_similar,
        'assessment': assessment
    }

def plot_convergence_comparison(numpy_result, pytorch_result, save_path=None):
    """Plot convergence comparison"""
    if not (numpy_result['success'] and pytorch_result['success']):
        print("Cannot plot - one or both optimizations failed")
        return
    
    plt.figure(figsize=(12, 5))
    
    # Plot loss curves
    plt.subplot(1, 2, 1)
    numpy_epochs = range(len(numpy_result['history']))
    pytorch_epochs = range(len(pytorch_result['history']))
    
    plt.plot(numpy_epochs, numpy_result['history'], 'r-', linewidth=2, label='NumPy', alpha=0.8)
    plt.plot(pytorch_epochs, pytorch_result['history'], 'b-', linewidth=2, label='PyTorch', alpha=0.8)
    
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training Loss Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.yscale('log')
    
    # Plot final comparison
    plt.subplot(1, 2, 2)
    metrics = ['Final Loss', 'Correlation', 'Training Time (s)']
    numpy_values = [numpy_result['final_loss'], numpy_result['correlation'], numpy_result['training_time']]
    pytorch_values = [pytorch_result['final_loss'], pytorch_result['correlation'], pytorch_result['training_time']]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    # Normalize values for comparison
    numpy_norm = [v / max(nv, pv) for v, nv, pv in zip(numpy_values, numpy_values, pytorch_values)]
    pytorch_norm = [v / max(nv, pv) for v, nv, pv in zip(pytorch_values, numpy_values, pytorch_values)]
    
    plt.bar(x - width/2, numpy_norm, width, label='NumPy', alpha=0.8, color='red')
    plt.bar(x + width/2, pytorch_norm, width, label='PyTorch', alpha=0.8, color='blue')
    
    plt.xlabel('Metrics')
    plt.ylabel('Normalized Values')
    plt.title('Performance Comparison')
    plt.xticks(x, metrics, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Plot saved to {save_path}")
    
    plt.show()

def run_optimization_comparison(epochs=100, n_obs=200):
    """Run comprehensive optimization comparison"""
    print("="*80)
    print("OPTIMIZATION COMPARISON TEST")
    print("="*80)
    
    if not NUMPY_AVAILABLE:
        print("❌ Cannot run comparison - original NumPy implementation not available")
        return None
    
    # Create test data
    print("Creating identical test data...")
    x, y, y_clean, params_true, args, nfeatures, layers = create_identical_test_data(
        seed=565, n_obs=n_obs, noise_level=0.05
    )
    
    print(f"Data shape: x={x.shape}, y={y.shape}")
    print(f"Model: {layers} layers, {nfeatures} features")
    print(f"Training epochs: {epochs}")
    
    # Test NumPy optimization
    numpy_result = test_numpy_optimization(x, y, params_true, args, nfeatures, layers, epochs)
    
    # Test PyTorch optimization
    pytorch_result = test_pytorch_optimization(x, y, params_true, args, nfeatures, layers, epochs)
    
    # Compare results
    comparison = compare_optimization_results(numpy_result, pytorch_result)
    
    # Plot comparison
    try:
        plot_convergence_comparison(numpy_result, pytorch_result)
    except Exception as e:
        print(f"Plotting failed: {e}")
    
    return {
        'numpy_result': numpy_result,
        'pytorch_result': pytorch_result,
        'comparison': comparison
    }

if __name__ == "__main__":
    # Run with different configurations
    print("Running optimization comparison with 100 epochs...")
    results = run_optimization_comparison(epochs=100, n_obs=200)
    
    if results and results['comparison']:
        print("\n" + "="*80)
        print("FINAL ASSESSMENT")
        print("="*80)
        print(results['comparison']['assessment'])
    else:
        print("❌ Optimization comparison failed")
