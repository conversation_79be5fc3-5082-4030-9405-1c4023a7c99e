#!/usr/bin/env python3
"""
Simple test script for PyTorch DNN implementation
Tests core functionality without visualization
"""

import torch
import numpy as np
from pytorch_dnn import (
    GaussianNet, RationalNet, DirichletNet, ReparameterizedRationalNet,
    DNNConfig, DNNDataset, DNNTrainer, DNNEvaluator,
    L1AbsLoss, L1AvgLoss, L2Loss,
    generate_data, create_model, create_loss_function,
    check_reparameterization
)

def test_basic_functionality():
    """Test basic model creation and forward pass"""
    print("Testing basic functionality...")
    
    # Test model creation
    config = DNNConfig()
    
    # Test GaussianNet
    gaussian_net = GaussianNet(4, 5, config.get_function_args('f0'))
    x_gaussian = torch.randn(5, 10)  # (nfeatures, nobs)
    y_gaussian = gaussian_net(x_gaussian)
    assert y_gaussian.shape == (10,), f"Expected shape (10,), got {y_gaussian.shape}"
    print("✓ GaussianNet test passed")
    
    # Test RationalNet
    rational_net = RationalNet(4, 5, config.get_function_args('f1'))
    x_rational = torch.randn(10)  # 1D input
    y_rational = rational_net(x_rational)
    assert y_rational.shape == (10,), f"Expected shape (10,), got {y_rational.shape}"
    print("✓ RationalNet test passed")
    
    # Test DirichletNet
    dirichlet_net = DirichletNet(3, 10, config.get_function_args('f2'), part='real')
    x_dirichlet = torch.randn(10)
    y_dirichlet = dirichlet_net(x_dirichlet)
    assert y_dirichlet.shape == (10,), f"Expected shape (10,), got {y_dirichlet.shape}"
    print("✓ DirichletNet test passed")
    
    print("All basic functionality tests passed!\n")

def test_loss_functions():
    """Test custom loss functions"""
    print("Testing loss functions...")
    
    y_true = torch.tensor([1.0, 2.0, 3.0, 4.0, 5.0])
    y_pred = torch.tensor([1.1, 1.9, 3.2, 3.8, 5.1])
    
    # Test L1 losses
    l1_abs = L1AbsLoss()
    l1_avg = L1AvgLoss()
    l2_loss = L2Loss()
    
    loss_abs = l1_abs(y_pred, y_true)
    loss_avg = l1_avg(y_pred, y_true)
    loss_l2 = l2_loss(y_pred, y_true)
    
    print(f"L1 Abs Loss: {loss_abs.item():.6f}")
    print(f"L1 Avg Loss: {loss_avg.item():.6f}")
    print(f"L2 Loss: {loss_l2.item():.6f}")
    
    assert loss_abs > 0, "L1 Abs loss should be positive"
    assert loss_avg > 0, "L1 Avg loss should be positive"
    assert loss_l2 > 0, "L2 loss should be positive"
    
    print("✓ All loss function tests passed!\n")

def test_data_generation():
    """Test data generation for different function types"""
    print("Testing data generation...")
    
    config = DNNConfig()
    config.update(n_observations=50, epochs=10)  # Small for testing
    
    function_types = ['f0', 'f1', 'f2']
    
    for func_type in function_types:
        print(f"Testing {func_type} data generation...")
        config.set_function_config(func_type)
        
        try:
            x, y, y_base, params, args = generate_data(func_type, config)
            
            # Basic checks
            assert len(y) == config.get('n_observations'), f"Wrong number of observations for {func_type}"
            assert not np.any(np.isnan(y)), f"NaN values in y for {func_type}"
            assert not np.any(np.isnan(y_base)), f"NaN values in y_base for {func_type}"
            
            print(f"✓ {func_type} data generation passed")
            
        except Exception as e:
            print(f"✗ {func_type} data generation failed: {str(e)}")
    
    print("Data generation tests completed!\n")

def test_training():
    """Test training functionality"""
    print("Testing training functionality...")
    
    config = DNNConfig()
    config.update(n_observations=50, epochs=20, print_every=1000)  # Small for testing
    
    # Test with f2 (simplest case)
    config.set_function_config('f2')
    
    # Generate data
    x, y, y_base, params, args = generate_data('f2', config)
    
    # Create dataset
    dataset = DNNDataset(x, y, distill_rate=1.0, seed=config.get('seed'))
    
    # Create model and loss
    model = create_model('f2', config.get('layers'), config.get('nfeatures'), args)
    loss_fn = create_loss_function(config.get('loss_type'))
    
    # Create trainer
    trainer = DNNTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer_type='L2',
        lr=config.get('learning_rate'),
        temperature=0.0
    )
    
    # Train
    history = trainer.train(dataset.x, dataset.y, epochs=20, print_every=1000)
    
    # Check training worked
    assert len(history) == 21, f"Expected 21 history entries, got {len(history)}"  # Initial + 20 epochs
    # Allow for small fluctuations in loss
    improvement_ratio = (history[0] - history[-1]) / history[0]
    assert improvement_ratio >= -0.1, f"Loss increased too much during training: {improvement_ratio:.3f}"
    
    print(f"Initial loss: {history[0]:.6f}")
    print(f"Final loss: {history[-1]:.6f}")
    print("✓ Training test passed!\n")

def test_evaluation():
    """Test evaluation functionality"""
    print("Testing evaluation functionality...")
    
    config = DNNConfig()
    config.update(n_observations=50, epochs=10)
    config.set_function_config('f2')
    
    # Generate data and train a simple model
    x, y, y_base, params, args = generate_data('f2', config)
    dataset = DNNDataset(x, y, distill_rate=1.0, seed=config.get('seed'))
    
    model = create_model('f2', config.get('layers'), config.get('nfeatures'), args)
    loss_fn = create_loss_function('L2')
    
    trainer = DNNTrainer(model, loss_fn, optimizer_type='L2', lr=0.1)
    trainer.train(dataset.x, dataset.y, epochs=10, print_every=1000)
    
    # Test evaluation
    evaluator = DNNEvaluator(model, loss_fn)
    results = evaluator.evaluate_model(dataset.x, dataset.y, params)
    
    # Check results
    required_keys = ['loss', 'mae', 'mse', 'rmse', 'correlation', 'r_squared']
    for key in required_keys:
        assert key in results, f"Missing key {key} in evaluation results"
        assert not np.isnan(results[key]), f"NaN value for {key}"
    
    print(f"Evaluation results:")
    for key in required_keys:
        print(f"  {key}: {results[key]:.6f}")
    
    print("✓ Evaluation test passed!\n")

def test_reparameterization():
    """Test reparameterization functionality"""
    print("Testing reparameterization...")
    
    config = DNNConfig()
    config.set_function_config('f1')
    
    # Generate test data
    x, y, y_base, params, args = generate_data('f1', config)
    x_tensor = torch.tensor(x, dtype=torch.float32)
    
    # Create networks
    original_net = RationalNet(config.get('layers'), config.get('nfeatures'), args)
    reparam_net = ReparameterizedRationalNet(config.get('layers'), config.get('nfeatures'), args)
    
    # Set parameters
    original_net.set_params_from_numpy(params)
    
    # Test reparameterization
    is_correct = check_reparameterization(original_net, reparam_net, x_tensor)
    
    assert is_correct, "Reparameterization test failed"
    print("✓ Reparameterization test passed!\n")

def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("PyTorch DNN Implementation - Comprehensive Tests")
    print("=" * 60)
    
    try:
        test_basic_functionality()
        test_loss_functions()
        test_data_generation()
        test_training()
        test_evaluation()
        test_reparameterization()
        
        print("=" * 60)
        print("🎉 ALL TESTS PASSED! 🎉")
        print("PyTorch DNN implementation is working correctly!")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Set random seeds for reproducibility
    torch.manual_seed(565)
    np.random.seed(565)
    
    run_all_tests()
